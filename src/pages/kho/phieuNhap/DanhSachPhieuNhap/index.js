import React, {
  useRef,
  useState,
  forwardRef,
  useImperative<PERSON><PERSON><PERSON>,
  useMemo,
} from "react";
import {
  HeaderSearch,
  TableWrapper,
  Pagination,
  Tooltip,
  Checkbox,
  AuthWrapper,
} from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import moment from "moment";
import { formatNumber, isArray } from "utils/index";
import { Main } from "./styled";
import { useEnum, useStore, useThietLap } from "hooks";
import {
  ENUM,
  FORMAT_DATE_TIME,
  LOAI_NHAP_XUAT,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_THUOC,
  TRANG_THAI_THANH_TOAN_NCC,
} from "constants/index";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import ModalDanhSachPhieu from "../NhaCungCap/ChiTiet/ModalDanhSachPhieu";
import { checkRole } from "lib-utils/role-utils";

const { Setting } = TableWrapper;

const listTrangThaiShowSoPhieuDoiUng = [
  LOAI_NHAP_XUAT.DU_TRU, //20
  LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO, //30
  LOAI_NHAP_XUAT.PHIEU_TRA, //70
  LOAI_NHAP_XUAT.LINH_BU_TU_TRUC, //80
];

const DanhSachPhieuNhap = ({ onInBienBanKiemNhap, ...props }, ref) => {
  const history = useHistory();
  const refSettings = useRef(null);
  const { t } = useTranslation();
  const refModalDanhSachPhieu = useRef(null);
  const {
    listPhieuNhap,
    totalElements,
    page,
    size,
    dataSortColumn = {},
    dataSearch,
  } = useStore("nhapKho", null, {
    fields:
      "listPhieuNhap, totalElements, page, size, dataSortColumn, dataSearch",
  });
  const phieuNhapXuatId = useStore("nhapKhoChiTiet.phieuNhapXuatId");
  const listKhoUser = useStore("kho.listKhoUser", []);
  const [state, _setState] = useState({
    isCheckedAll: false,
    dsId: [],
    selectedRowKeys: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const {
    nhapKho: { getListPhieuNhap, onSizeChange },
    nhapKhoChiTiet: { updateData: updateDataNhapKho },
  } = useDispatch();
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);
  const [dataSO_PHIEU_NHAP_XUAT_KHO] = useThietLap(
    THIET_LAP_CHUNG.SO_PHIEU_NHAP_XUAT_KHO
  );
  const [dataTACH_DAI_PHIEU_LINH_TRA] = useThietLap(
    THIET_LAP_CHUNG.TACH_DAI_PHIEU_LINH_TRA
  );

  const { loaiNhapXuat } = getAllQueryString();

  useImperativeHandle(ref, () => ({
    getData: () => {
      return state.dsId;
    },
  }));

  const isTachSoPhieu = useMemo(() => {
    return [1, 2, 3, 4].includes(parseInt(dataSO_PHIEU_NHAP_XUAT_KHO));
  }, [dataSO_PHIEU_NHAP_XUAT_KHO]);

  const onClickSort = (key, value) => {
    if (isArray(listKhoUser, true)) {
      getListPhieuNhap({ dataSortColumn: { ...dataSortColumn, [key]: value } });
    }
  };

  const onChangePage = (page) => {
    if (isArray(listKhoUser, true)) {
      setQueryStringValue("page", page - 1);
      getListPhieuNhap({ page: page - 1 });
    }
  };

  const handleSizeChange = (size) => {
    if (isArray(listKhoUser, true)) {
      setQueryStringValues({ size: size, page: 0 });
      onSizeChange({ size });
    }
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        let path = "/kho/phieu-nhap-nha-cung-cap/chi-tiet/";
        switch (record.loaiNhapXuat) {
          case 10: //nha cung cap
            path = "/kho/phieu-nhap-nha-cung-cap/chi-tiet/";
            break;
          case 12: //nhập khác
            path = "/kho/phieu-nhap-khac/chi-tiet/";
            break;
          case 20: //du tru
            path = "/kho/phieu-nhap-du-tru/chi-tiet/";
            break;
          case 30: //chuyen kho
            path = "/kho/phieu-nhap-chuyen-kho/chi-tiet/";
            break;
          case 70: //nha cung cap
            path = "/kho/phieu-nhap-nha-cung-cap/chi-tiet/";
            break;
          case 80: //chuyen kho
            path = "/kho/nhap-kho/chi-tiet-linh-bu/";
            break;
          case 45: //dao han su dung
            path = "/kho/phieu-nhap-dao-han-su-dung/chi-tiet/";
            break;
          default:
            break;
        }
        const { id } = record;
        history.push({
          pathname: `${path}${id}`,
          state: getAllQueryString(),
        });
        updateDataNhapKho({
          phieuNhapXuatId: id,
          currentItem: { ...record },
        });
      },
    };
  };

  const setRowClassName = (record) => {
    let idDiff;
    idDiff = phieuNhapXuatId;
    return record.id === idDiff ? "row-selected-detail" : "";
  };

  const onScanChungTu = (item) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    refModalDanhSachPhieu.current &&
      refModalDanhSachPhieu.current.show(item.id);
  };

  const widthStt = () => {
    const num = String((page + 1) * size).length;
    if (num >= 4) {
      return Number(num) * 10;
    }
    return 35;
  };

  const renderTrangThaiDls = (data) => {
    const { trangThaiDls } = data || {};
    let title = "";
    if (trangThaiDls) {
      title = listTrangThaiDls?.find((item) => item.id === trangThaiDls)?.i18;
      if (title) return t(title);
    } else {
      return t("kho.choDuyetDLS");
    }
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      isCheckedAll: listPhieuNhap.length === selectedRowKeys.length,
      dsId: selectedRowKeys,
      selectedRowKeys,
    });
  };

  const oncheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked ? listPhieuNhap.map((x) => x.id) : [],
      isCheckedAll: e.target?.checked,
      dsId: e.target?.checked ? listPhieuNhap.map((x) => x.id) : [],
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={state.isCheckedAll}
          />
        }
      />
    ),
    columnWidth: 30,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const tenSoPhieuDoiUng = (() => {
    if (
      [LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO, LOAI_NHAP_XUAT.PHIEU_TRA].includes(
        dataSearch?.loaiNhapXuat
      )
    ) {
      return "kho.soPhieuNhap";
    }
    if (
      [LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        dataSearch?.loaiNhapXuat
      )
    ) {
      return "baoCao.soPhieuXuat";
    }
    return "goiDichVu.soPhieuDoiUng";
  })();

  const isShowSoPhieuDoiUng = (() => {
    if (dataSearch.loaiNhapXuat) {
      return (
        isTachSoPhieu &&
        (dataSearch.loaiNhapXuat === LOAI_NHAP_XUAT.PHIEU_TRA
          ? dataTACH_DAI_PHIEU_LINH_TRA?.eval()
          : true) &&
        listTrangThaiShowSoPhieuDoiUng.includes(dataSearch.loaiNhapXuat)
      );
    } else {
      return isTachSoPhieu;
    }
  })();

  const showThanhToanNCC = useMemo(() => {
    return (
      checkRole([ROLES["KHO"].HIEN_THI_CHECK_BOX_THANH_TOAN_NCC]) &&
      dataSearch?.loaiNhapXuat === LOAI_NHAP_XUAT.NHAP_TU_NCC
    );
  }, [dataSearch]);

  const columns = [
    {
      title: <HeaderSearch title="STT" />,
      width: widthStt(),
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soPhieu")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "common.soPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t(tenSoPhieuDoiUng)}
          sort_key="soPhieuDoiUng"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soPhieuDoiUng"] || ""}
        />
      ),
      width: "120px",
      dataIndex: "soPhieuDoiUng",
      key: "soPhieuDoiUng",
      align: "center",
      i18Name: tenSoPhieuDoiUng,
      show: true,
      hidden: !isShowSoPhieuDoiUng,
      render: (item, data) => {
        if (listTrangThaiShowSoPhieuDoiUng.includes(data.loaiNhapXuat)) {
          return item;
        }
        return "";
      },
    },
    {
      title: <HeaderSearch title={t("kho.khoNhap")} />,
      width: "100px",
      dataIndex: "tenKho",
      key: "khoNhap",
      i18Name: "kho.khoNhap",
      show: true,
      render: (item, data) =>
        [
          LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
          LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG,
        ].includes(data?.loaiNhapXuat)
          ? data.tenKhoDoiUng
          : item,
    },
    {
      title: <HeaderSearch title={t("kho.khoXuat")} />,
      width: "100px",
      dataIndex: "tenKhoDoiUng",
      key: "khoXuat",
      i18Name: "kho.khoXuat",
      show: true,
      render: (item, data) =>
        [
          LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
          LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG,
        ].includes(data?.loaiNhapXuat)
          ? data.tenKho
          : item,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.phieuTraLan")}
          sort_key="lan"
          dataSort={dataSortColumn["lan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "50px",
      dataIndex: "lan",
      key: "lan",
      hidden: dataSearch?.loaiNhapXuat != 70, //loại phiếu trả
      i18Name: "kho.phieuTraLan",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.thanhTien")}
          sort_key="thanhTien"
          dataSort={dataSortColumn["thanhTien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      i18Name: "common.thanhTien",
      show: true,
      render: (field, _, __) =>
        (field && formatNumber(Number.parseFloat(`${field}`), 3)) || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("khoMau.thanhTienSuaDoi")}
          sort_key="thanhTienSuaDoi"
          dataSort={dataSortColumn["thanhTienSuaDoi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "90px",
      dataIndex: "thanhTienSuaDoi",
      key: "thanhTienSuaDoi",
      align: "right",
      i18Name: "khoMau.thanhTienSuaDoi",
      show: true,
      render: (field, _, __) =>
        (field && formatNumber(Number.parseFloat(`${field}`), 3)) || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.trangThai")}
          sort_key="trangThai"
          dataSort={dataSortColumn["trangThai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "60px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "common.trangThai",
      show: true,
      render: (item) => {
        return listTrangThaiPhieuNhapXuat.find((x) => x.id === item)?.ten;
      },
    },

    {
      title: (
        <HeaderSearch
          title={t("kho.trangThaiDuyetDls")}
          sort_key="trangThaiDls"
          dataSort={dataSortColumn["trangThaiDls"] || ""}
          onClickSort={onClickSort}
        />
      ),
      show: true,
      width: 100,
      dataIndex: "trangThaiDls",
      key: "trangThaiDls",
      i18Name: "kho.trangThaiDuyetDls",
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        Number(loaiNhapXuat)
      ),
      render: (_, item) => renderTrangThaiDls(item),
    },
    {
      title: <HeaderSearch title={t("common.loaiChiDinh")} />,
      width: 120,
      dataIndex: "loaiChiDinh",
      key: "loaiChiDinh",
      align: "center",
      i18Name: t("common.loaiChiDinh"),
      show: true,
      hidden:
        !loaiNhapXuat ||
        (loaiNhapXuat &&
          ![LOAI_NHAP_XUAT.LINH_BU_TU_TRUC, LOAI_NHAP_XUAT.PHIEU_TRA]?.includes(
            Number(loaiNhapXuat)
          )),
      render: (item) => {
        return listLoaiChiDinh.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hinhThucNhap")}
          sort_key="tenHinhThucNhapXuat"
          dataSort={dataSortColumn["tenHinhThucNhapXuat"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 100,
      dataIndex: "tenHinhThucNhapXuat",
      key: "tenHinhThucNhapXuat",
      i18Name: "kho.hinhThucNhap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dieuChinhCoSoTren")}
          sort_key="dieuChinhCoSo"
          dataSort={dataSortColumn["dieuChinhCoSo"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 60,
      dataIndex: "dieuChinhCoSo",
      key: "dieuChinhCoSo",
      i18Name: "kho.dieuChinhCoSoTren",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].includes(
        Number(loaiNhapXuat)
      ),
      render: (field) => (
        <div className="flex-center">
          <Checkbox defaultChecked={!!field} />
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dieuChinhCoSoDuoi")}
          sort_key="dieuChinhCoSoDuoi"
          dataSort={dataSortColumn["dieuChinhCoSoDuoi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 60,
      dataIndex: "dieuChinhCoSoDuoi",
      key: "dieuChinhCoSoDuoi",
      i18Name: "kho.dieuChinhCoSoDuoi",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].includes(
        Number(loaiNhapXuat)
      ),
      render: (field) => (
        <div className="flex-center">
          <Checkbox defaultChecked={!!field} />
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soHoaDon")}
          sort_key="soHoaDon"
          dataSort={dataSortColumn["soHoaDon"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "soHoaDon",
      key: "soHoaDon",
      i18Name: "kho.soHoaDon",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.ngayHoaDon")}
          sort_key="ngayHoaDon"
          dataSort={dataSortColumn["ngayHoaDon"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "ngayHoaDon",
      key: "ngayHoaDon",
      i18Name: "kho.ngayHoaDon",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soHopDong")}
          sort_key="soHopDong"
          dataSort={dataSortColumn["soHopDong"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "soHopDong",
      key: "soHopDong",
      i18Name: "kho.soHopDong",
      show:
        !loaiNhapXuat ||
        [LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(Number(loaiNhapXuat)),
      hidden:
        loaiNhapXuat &&
        ![LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(Number(loaiNhapXuat)),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.ngayHopDong")}
          sort_key="ngayHopDong"
          dataSort={dataSortColumn["ngayHopDong"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "ngayHopDong",
      key: "ngayHopDong",
      i18Name: "kho.quyetDinhThau.ngayHopDong",
      show:
        !loaiNhapXuat ||
        [LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(Number(loaiNhapXuat)),
      hidden:
        loaiNhapXuat &&
        ![LOAI_NHAP_XUAT.NHAP_TU_NCC].includes(Number(loaiNhapXuat)),
      render: (item) => item && moment(item).format("DD/MM/YYYY"),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.nhaCungCap")}
          sort_key="tenNhaCungCap"
          dataSort={dataSortColumn["tenNhaCungCap"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenNhaCungCap",
      key: "tenNhaCungCap",
      i18Name: "kho.nhaCungCap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thangDuTru")}
          sort_key="thangDuTru"
          dataSort={dataSortColumn["thangDuTru"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "thangDuTru",
      key: "thangDuTru",
      align: "right",
      i18Name: "kho.thangDuTru",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.quyetDinhThau.title")}
          sort_key="quyetDinhThau"
          dataSort={dataSortColumn["quyetDinhThau"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      i18Name: "kho.quyetDinhThau.title",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.nguonNhapKho")}
          sort_key="tenNguonNhapKho"
          dataSort={dataSortColumn["tenNguonNhapKho"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenNguonNhapKho",
      key: "tenNguonNhapKho",
      i18Name: "kho.nguonNhapKho",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.csytChuyenToi")}
          sort_key="tenCsKcbChuyenGiao"
          dataSort={dataSortColumn["tenCsKcbChuyenGiao"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "tenCsKcbChuyenGiao",
      key: "tenCsKcbChuyenGiao",
      i18Name: "kho.csytChuyenToi",
      show: false,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoaTaoPhieu")}
          sort_key="tenKhoaChiDinh"
          dataSort={dataSortColumn["tenKhoaChiDinh"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "100px",
      dataIndex: "tenKhoaChiDinh",
      key: "tenKhoaChiDinh",
      i18Name: "kho.khoaTaoPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("khoMau.ngayTaoPhieu")}
          sort_key="thoiGianTaoPhieu"
          dataSort={dataSortColumn["thoiGianTaoPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "thoiGianTaoPhieu",
      key: "thoiGianTaoPhieu",
      i18Name: "khoMau.ngayTaoPhieu",
      show: true,
      render: (item) => {
        return item && moment(item).format(FORMAT_DATE_TIME);
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.nguoiTaoPhieu")}
          sort_key="tenNguoiTaoPhieu"
          dataSort={dataSortColumn["tenNguoiTaoPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "tenNguoiTaoPhieu",
      key: "tenNguoiTaoPhieu",
      i18Name: "kho.nguoiTaoPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("khoMau.ngayDuyetPhieu")}
          sort_key="thoiGianDuyet"
          dataSort={dataSortColumn["thoiGianDuyet"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "180px",
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      i18Name: "khoMau.ngayDuyetPhieu",
      show: true,
      render: (item) => {
        return item && moment(item).format(FORMAT_DATE_TIME);
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="ghiChu"
          dataSort={dataSortColumn["ghiChu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "ghiChu",
      key: "ghiChu",
      i18Name: "common.ghiChu",
      show: false,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.daThanhToanNcc")}
          sort_key="thanhToanNcc"
          dataSort={dataSortColumn["thanhToanNcc"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "80px",
      dataIndex: "thanhToanNcc",
      key: "thanhToanNcc",
      i18Name: "kho.daThanhToanNcc",
      show: true,
      align: "center",
      hidden: !showThanhToanNCC,
      render: (item) => (
        <Checkbox checked={item === TRANG_THAI_THANH_TOAN_NCC.DA_THANH_TOAN} />
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("hoaHong.thoiGianThanhToan")}
          sort_key="thoiGianThanhToan"
          dataSort={dataSortColumn["thoiGianThanhToan"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "150px",
      dataIndex: "thoiGianThanhToan",
      key: "thoiGianThanhToan",
      i18Name: "hoaHong.thoiGianThanhToan",
      show: true,
      align: "center",
      hidden: !showThanhToanNCC,
      render: (item) => {
        return item && moment(item).format(FORMAT_DATE_TIME);
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.taiKhoanThanhToan")}
          sort_key="tenNguoiThanhToanNcc"
          dataSort={dataSortColumn["tenNguoiThanhToanNcc"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: "120px",
      dataIndex: "tenNguoiThanhToanNcc",
      key: "tenNguoiThanhToanNcc",
      i18Name: "kho.taiKhoanThanhToan",
      show: true,
      align: "center",
      hidden: !showThanhToanNCC,
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "70px",
      align: "center",
      fixed: "right",
      render: (_, item) => {
        return (
          <div>
            {dataSearch?.loaiNhapXuat == 10 && (
              <AuthWrapper accessRoles={[ROLES["BAO_CAO"].K08]}>
                <Tooltip
                  title={t("kho.inBienBanKiemNhap")}
                  placement="bottomLeft"
                >
                  <SVG.IcPrint
                    className="ic-action"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onInBienBanKiemNhap(item?.id);
                    }}
                  />
                </Tooltip>
              </AuthWrapper>
            )}

            {item?.dsChungTu != null && (
              <Tooltip title={t("kho.scanChungTu")}>
                <SVG.IcScanBieuMau
                  color={"var(--color-blue-primary)"}
                  className={`ic-action`}
                  onClick={onScanChungTu(item)}
                />
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        ref={refSettings}
        dataSource={listPhieuNhap}
        tableName="table_DSPhieuNhapKho"
        onRow={onRow}
        rowKey={(record) => record.id}
        rowClassName={setRowClassName}
        scroll={{ x: 2200 }}
        rowSelection={rowSelection}
      />
      <Pagination
        onChange={onChangePage}
        current={page + 1}
        pageSize={size}
        listData={listPhieuNhap}
        total={totalElements}
        onShowSizeChange={handleSizeChange}
      />
      <ModalDanhSachPhieu ref={refModalDanhSachPhieu} />
    </Main>
  );
};

export default forwardRef(DanhSachPhieuNhap);
