import React from "react";
import { SVG } from "assets";
import { NHOM_DANH_MUC, NHOM_BAO_CAO, ROLES } from "constants/index";
import { generatePhcnYhctTitle } from "pages/trangChu/utils";

export const ListContentLeftFunc = [
  {
    i18n: "danhMuc.danhMucLoaiCapCuu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-cap-cuu",
    accessRoles: [ROLES["DANH_MUC"].LOAI_CC],
    group: NHOM_DANH_MUC.CAP_CUU,
  },
  {
    i18n: "danhMuc.danhMucTaiNanThuongTich",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/tai-nan-thuong-tich",
    accessRoles: [ROLES["DANH_MUC"].TAI_NAN_THUONG_TICH],
    group: NHOM_DANH_MUC.CAP_CUU,
  },
  {
    i18n: "danhMuc.danhMucThoiGianCapCuu",
    icon: require("assets/images/pagehome/icChucNang2.png"),
    link: "/danh-muc/thoi-gian-cap-cuu",
    accessRoles: [ROLES["DANH_MUC"].TG_CC],
    group: NHOM_DANH_MUC.CAP_CUU,
  },
  {
    i18n: "danhMuc.danhMucViTriChanThuong",
    icon: require("assets/images/pagehome/icChucNang2.png"),
    link: "/danh-muc/vi-tri-chan-thuong",
    accessRoles: [ROLES["DANH_MUC"].VI_TRI_CHAN_THUONG],
    group: NHOM_DANH_MUC.CAP_CUU,
  },
  {
    i18n: "danhMuc.danhMucHoaChat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hoa-chat",
    accessRoles: [ROLES["DANH_MUC"].HOA_CHAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucChePhamMau",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/che-pham-mau",
    accessRoles: [ROLES["DANH_MUC"].CHE_PHAM_MAU],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucDoiTac",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/doi-tac",
    accessRoles: [ROLES["DANH_MUC"].NHA_SAN_XUAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucChePhamDinhDuong.title",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/che-pham-dinh-duong",
    accessRoles: [ROLES["DANH_MUC"].CHE_PHAM_DINH_DUONG],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucDuongDung",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/duong-dung",
    accessRoles: [ROLES["DANH_MUC"].DUONG_DUNG],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucHinhThucLoaiNhapXuat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hinh-thuc-nhap-xuat",
    accessRoles: [ROLES["DANH_MUC"].HINH_THUC_NHAP_XUAT_LOAI_XUAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucHoatChat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hoat-chat",
    accessRoles: [ROLES["DANH_MUC"].HOAT_CHAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucTacNhanDiUng",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/tac-nhan-di-ung",
    accessRoles: [],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucKhangNguyen",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/khang-nguyen",
    accessRoles: [ROLES["DANH_MUC"].KHANG_NGUYEN],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucLieuDung",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/lieu-dung",
    accessRoles: [ROLES["DANH_MUC"].LIEU_DUNG],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucLieuDungBacSi",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/lieu-dung-bac-si",
    group: NHOM_DANH_MUC.KHO,
    accessRoles: [ROLES["DANH_MUC"].LIEU_DUNG_BS],
  },
  {
    i18n: "danhMuc.danhMucLoiDan",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loi-dan",
    accessRoles: [ROLES["DANH_MUC"].LOI_DAN],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucNguonNhapKho",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nguon-nhap-kho",
    accessRoles: [ROLES["DANH_MUC"].NGUON_NHAP_KHO],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucNhomHoaChat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-hoa-chat",
    accessRoles: [ROLES["DANH_MUC"].NHOM_HOA_CHAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucNhomThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-thuoc",
    accessRoles: [ROLES["DANH_MUC"].NHOM_THUOC],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucNhomVatTu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-vat-tu",
    accessRoles: [ROLES["DANH_MUC"].NHOM_VAT_TU],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-loai-thuoc",
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_THUOC],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucPhanNhomThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-nhom-thuoc",
    accessRoles: [ROLES["DANH_MUC"].PHAN_NHOM_THUOC],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucThangSoBanLe",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/thang-so-ban-le",
    accessRoles: [ROLES["DANH_MUC"].DINH_MUC_THANG_SO],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/thuoc",
    accessRoles: [ROLES["DANH_MUC"].THUOC],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucThuocKeNgoai",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/thuoc-ke-ngoai",
    group: NHOM_DANH_MUC.KHO,
    accessRoles: [ROLES["DANH_MUC"].THUOC_KE_NGOAI],
  },
  {
    i18n: "danhMuc.danhMucVatTu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/vat-tu",
    accessRoles: [ROLES["DANH_MUC"].VAT_TU],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucXuatXu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/xuat-xu",
    accessRoles: [ROLES["DANH_MUC"].XUAT_XU],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucKhoaDuLieuBaoCaoKho",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/khoa-du-lieu-bao-cao-kho",
    accessRoles: [ROLES["DANH_MUC"].KHOA_DU_LIEU_BAO_CAO_KHO],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiVTYT",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-loai-vat-tu",
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_VAT_TU],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucThuocVatTuBanGiao",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/thuoc-vat-tu-ban-giao",
    accessRoles: [ROLES["DANH_MUC"].THUOC_VAT_TU_BAN_GIAO],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucBaoCao",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/bao-cao",
    accessRoles: [ROLES["DANH_MUC"].BAO_CAO],
    group: NHOM_DANH_MUC.KY_IN_PHIEU,
  },
  {
    i18n: "danhMuc.danhMucLoaiPhieu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-phieu",
    group: NHOM_DANH_MUC.KY_IN_PHIEU,
    accessRoles: [ROLES["DANH_MUC"].LOAI_PHIEU],
  },
  {
    i18n: "danhMuc.danhMucMayIn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/may-in",
    accessRoles: [ROLES["DANH_MUC"].MAY_IN],
    group: NHOM_DANH_MUC.KY_IN_PHIEU,
  },
  {
    i18n: "danhMuc.danhMucQuyenKy",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/quyen-ky",
    accessRoles: [ROLES["DANH_MUC"].QUYEN_KY],
    group: NHOM_DANH_MUC.KY_IN_PHIEU,
  },
  {
    i18n: "danhMuc.danhMucBenhPham",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/benh-pham",
    accessRoles: [ROLES["DANH_MUC"].BENH_PHAM],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucBoChiDinh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/bo-chi-dinh",
    accessRoles: [ROLES["DANH_MUC"].BO_CHI_DINH],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucChuyenKhoa",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/chuyen-khoa",
    accessRoles: [ROLES["DANH_MUC"].CHUYEN_KHOA],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucSuatAn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/suat-an",
    accessRoles: [ROLES["DANH_MUC"].DICH_VU_AN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDichVuCDHAVaTDCN",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dich-vu-cdha-tdcn",
    accessRoles: [ROLES["DANH_MUC"].CDHA_TDCN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDichVuGiuong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/dich-vu-giuong",
    group: NHOM_DANH_MUC.DICH_VU,
    accessRoles: [],
  },
  {
    i18n: "danhMuc.danhMucDichVuKhamBenh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dich-vu-kham-benh",
    accessRoles: [ROLES["DANH_MUC"].DICH_VU_KHAM_BENH],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDichVuTongHop",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dich-vu-tong-hop",
    accessRoles: [ROLES["DANH_MUC"].DICH_VU_TONG_HOP],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDichVuNgoaiDieuTri",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/ngoai-dieu-tri",
    group: NHOM_DANH_MUC.DICH_VU,
    accessRoles: [ROLES["DANH_MUC"].DV_NGOAI_DIEU_TRI],
  },
  {
    i18n: "danhMuc.danhMucDichVuPTTT",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dich-vu-phau-thuat",
    accessRoles: [ROLES["DANH_MUC"].DV_PHAU_THUAT_THU_THUAT],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDichVuXetNghiem",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dich-vu-xet-nghiem",
    accessRoles: [ROLES["DANH_MUC"].DICH_VU_XN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucDonViTinh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/don-vi-tinh",
    accessRoles: [ROLES["DANH_MUC"].DON_VI_TINH],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucLyDoDoiTraDichVu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ly-do-tra-dv",
    accessRoles: [ROLES["DANH_MUC"].DOI_TRA_DICH_VU],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucMauKetQuaCDHAVaTDCN",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-ket-qua-cls",
    accessRoles: [ROLES["DANH_MUC"].MAU_KQ_CDHA_TDCN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucMauKetQuaXetNghiem",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-ket-qua-xet-nghiem",
    accessRoles: [ROLES["DANH_MUC"].MAU_KET_QUA_XN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucMauKetQuaXNCoDotBien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-kq-xn-dot-bien",
    accessRoles: [ROLES["DANH_MUC"].MAU_KQ_XN_DOT_BIEN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucMauKetQuaKhamBenh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-ket-qua-kham-benh",
    accessRoles: [ROLES["DANH_MUC"].MAU_KET_QUA_KHAM_BENH],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucNhomChiSo",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-chi-so",
    accessRoles: [ROLES["DANH_MUC"].NHOM_CHI_SO],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucNhomDichVu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-dich-vu",
    accessRoles: [ROLES["DANH_MUC"].NHOM_DICH_VU],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucNoiLayMauBenhPham",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/noi-lay-benh-pham",
    accessRoles: [ROLES["DANH_MUC"].NOI_LAY_BENH_PHAM],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucPhuongPhapCheBien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phuong-phap-che-bien",
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_CHE_BIEN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucPhuongPhapVoCam",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phuong-phap-vo-cam",
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_VO_CAM],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucPhuongPhapNhuom",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phuong-phap-nhuom",
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_NHUOM],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucViTriSinhThiet",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/vi-tri-sinh-thiet",
    accessRoles: [ROLES["DANH_MUC"].VI_TRI_SINH_THIET],
    group: NHOM_DANH_MUC.DICH_VU,
  },

  {
    i18n: "danhMuc.danhMucBenhVien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/benh-vien",
    accessRoles: [ROLES["DANH_MUC"].BENH_VIEN],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucChucVu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/chuc-vu",
    accessRoles: [ROLES["DANH_MUC"].CHUC_VU],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucCoQuanDonVi",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/co-quan-don-vi",
    accessRoles: [ROLES["DANH_MUC"].CO_QUAN],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucDanToc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dan-toc",
    accessRoles: [ROLES["DANH_MUC"].DAN_TOC],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucDiaChiHanhChinh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dia-chi-hanh-chinh",
    accessRoles: [ROLES["DANH_MUC"].DIA_CHI_HANH_CHINH],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucDonViChiNhanh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/don-vi-chi-nhanh",
    accessRoles: [],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucDiTatBamSinh.title",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/di-tat-bam-sinh",
    accessRoles: [ROLES["DANH_MUC"].DI_TAT_BAM_SINH],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucHocHamHocVi",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hoc-ham-hoc-vi",
    accessRoles: [ROLES["DANH_MUC"].HOC_HAM],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucKhuVuc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/khu-vuc",
    accessRoles: [ROLES["DANH_MUC"].KHU_VUC],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucMoiQuanHe",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/moi-quan-he",
    accessRoles: [ROLES["DANH_MUC"].MOI_QUAN_HE],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucNgheNghiep",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nghe-nghiep",
    accessRoles: [ROLES["DANH_MUC"].NGHE_NGHIEP],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucNguoiDaiDien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nguoi-dai-dien",
    accessRoles: [ROLES["DANH_MUC"].NGUOI_DAI_DIEN],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucNhanTheoDoi",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhan-theo-doi",
    accessRoles: [ROLES["DANH_MUC"].NHAN_THEO_DOI],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucQuanHam",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/quan-ham",
    accessRoles: [ROLES["DANH_MUC"].QUAN_HAM],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucVanBangChuyenMon",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/van-bang-chuyen-mon",
    accessRoles: [ROLES["DANH_MUC"].VAN_BANG],
    group: NHOM_DANH_MUC.HANH_CHINH,
  },

  {
    i18n: "danhMuc.danhMucChiPhiHapSayVTYTTaiSuDung",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/chi-phi-hap-say-vtyt-tai-su-dung",
    accessRoles: [ROLES["DANH_MUC"].CHI_PHI_HAP_SAY],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucBacSiNgoaiVien",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/bac-si-ngoai-vien",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].BAC_SI_NGOAI_VIEN],
  },
  {
    i18n: "danhMuc.danhMucCaLamViec",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ca-lam-viec",
    accessRoles: [ROLES["DANH_MUC"].CA_LAM_VIEC],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucAnhLuocDoPhauThuat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/luoc-do-pt",
    accessRoles: [ROLES["DANH_MUC"].ANH_LUOC_DO_PT],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucCauHoiKhamSangLoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/cau-hoi-kham-sang-loc",
    accessRoles: [ROLES["DANH_MUC"].CAU_HOI_KHAM_SANG_LOC],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucCheDoChamSoc",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/che-do-cham-soc",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].CHE_DO_CHAM_SOC],
  },
  {
    i18n: "danhMuc.danhMucChiSoSong",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/chi-so-song",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucDacTinhDuocLy",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dac-tinh-duoc-ly",
    accessRoles: [ROLES["DANH_MUC"].DAC_TINH_DUOC_LY],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucGoiMo10Ngay",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/goi-pt-tt",
    accessRoles: [ROLES["DANH_MUC"].GOI_MO_10_NGAY],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucHauQuaTuongTac",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hau-qua-tuong-tac",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucHoiDong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/hoi-dong",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].HOI_DONG],
  },
  {
    i18n: "danhMuc.danhMucKhoa",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/khoa",
    accessRoles: [ROLES["DANH_MUC"].KHOA],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucKiosk",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/kiosk",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["THIET_LAP"].KIOSK],
  },
  {
    i18n: "danhMuc.danhMucLoaGoiSo",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loa-goi-so",
    accessRoles: [ROLES["DANH_MUC"].LOA_GOI_SO],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucLoaiBenhAn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-benh-an",
    accessRoles: [ROLES["DANH_MUC"].LOAI_BA],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucLoaiBuaAn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-bua-an",
    accessRoles: [ROLES["DANH_MUC"].LOAI_BUA_AN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucLoaiDoiTuong",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-doi-tuong",
    accessRoles: [ROLES["DANH_MUC"].LOAI_DOI_TUONG],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucLoaiGiuong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/loai-giuong",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [],
  },
  {
    i18n: "danhMuc.danhMucLoaiHinhThanhToan",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-hinh-thanh-toan",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucLyDoTamUng",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ly-do-tam-ung",
    accessRoles: [ROLES["DANH_MUC"].LY_DO_TAM_UNG],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMaMay",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ma-may",
    accessRoles: [ROLES["DANH_MUC"].MA_MAY],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMaPTTTQuocTe",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ma-pttt-quoc-te",
    accessRoles: [ROLES["DANH_MUC"].PTTT_QUOC_TE],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMaPhieuLinh",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/phieu-linh",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].MA_PHIEU_LINH],
  },
  {
    i18n: "danhMuc.danhMucMauBenhAnVaoVien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-benh-an-vao-vien",
    accessRoles: [ROLES["DANH_MUC"].MAU_BENH_AN_VAO_VIEN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMauDienBien",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-dien-bien",
    accessRoles: [ROLES["DANH_MUC"].MAU_DIEN_BIEN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMauKetQuaPTTT",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/mau-kq-pt-tt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].MAU_KQ_PT_TT],
  },
  {
    i18n: "danhMuc.danhMucMauQMS",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/mau-qms",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].MAU_QMS],
  },
  {
    i18n: "danhMuc.danhMucMucDoTuongTac",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/muc-do-tuong-tac",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucMucDoBangChung",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/muc-do-bang-chung",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNgayNghiLe",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ngay-nghi-le",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNhomBenhTat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-benh-tat",
    accessRoles: [ROLES["DANH_MUC"].BENH_TAT],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNhomChiPhiCoSo",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-chi-phi",
    accessRoles: [ROLES["DANH_MUC"].NHOM_CHI_PHI],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiDanhGiaBMI",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-loai-bmi",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiNguoiBenh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-loai-nb",
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_NB],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiPHCN",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-loai-phcn",
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_PHCN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhieuIn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phieu-in",
    accessRoles: [ROLES["DANH_MUC"].MAN_HINH_PHIEU_IN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucKhaiBaoMauProtocolPttt",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/protocol",
    accessRoles: [ROLES["DANH_MUC"].PROTOCOL],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhong",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phong",
    accessRoles: [ROLES["DANH_MUC"].PHONG],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhuongThucThanhToan",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phuong-thuc-thanh-toan",
    accessRoles: [ROLES["DANH_MUC"].PTTT],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucQuay",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/quay",
    accessRoles: [ROLES["DANH_MUC"].QUAY],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucQuyTrinhXetNghiem",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/quy-trinh-xet-nghiem",
    accessRoles: [],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucSoHieuGiuong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/so-hieu-giuong",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [],
  },
  {
    i18n: "danhMuc.danhMucTaiLieuHDSD",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/huong-dan-su-dung",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].HDSD],
  },
  {
    i18n: "danhMuc.danhMucTheBaoHiem",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/the-bao-hiem",
    accessRoles: [ROLES["DANH_MUC"].THE_BAO_HIEM],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucToaNha",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/toa-nha",
    accessRoles: [ROLES["DANH_MUC"].NHA],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucVacXin",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/vac-xin",
    accessRoles: [ROLES["DANH_MUC"].VAC_XIN],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucKhaiBaoTuongTacThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/tuong-tac-thuoc",
    accessRoles: [ROLES["DANH_MUC"].TUONG_TAC_THUOC],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucHangBangLaiXe",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/hang-bang-lai-xe",
    accessRoles: [ROLES["DANH_MUC"].HANG_BANG_LAI_XE],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucDoiTuongKcb",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/doi-tuong-kcb",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DOI_TUONG_KCB],
  },
  {
    i18n: "danhMuc.danhMucChuongTrinhGiamGia",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/chuong-trinh-giam-gia",
    accessRoles: [ROLES["DANH_MUC"].CHUONG_TRINH_GIAM_GIA],
    group: NHOM_DANH_MUC.KHACH_HANG,
  },
  {
    i18n: "danhMuc.danhMucHangThe",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/hang-the",
    accessRoles: [ROLES["DANH_MUC"].HANG_THE],
    group: NHOM_DANH_MUC.KHACH_HANG,
  },
  {
    i18n: "danhMuc.danhMucNguonNguoiBenh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nguon-nguoi-benh",
    accessRoles: [ROLES["DANH_MUC"].NGUON_NGUOI_BENH],
    group: NHOM_DANH_MUC.KHACH_HANG,
  },
  {
    i18n: "danhMuc.danhMucThiLuc",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/thi-luc",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].THI_LUC],
  },
  {
    i18n: "danhMuc.danhMucXangDau",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/xang-dau",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].XANG_DAU],
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiPhuongPhapVoCam",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/phan-loai-phuong-phap-vo-cam",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_PHUONG_PHAP_VO_CAM],
  },
  {
    i18n: "danhMuc.danhMucBenhYHocCoTruyen",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/benh-y-hoc-co-truyen",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].BENH_Y_HOC_CO_TRUYEN],
  },
  {
    i18n: "danhMuc.danhMucLoaiNhiemKhuan",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/loai-nhiem-khuan",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].LOAI_NHIEM_KHUAN],
  },
  {
    i18n: "danhMuc.danhMucViKhuan",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/vi-khuan",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].VI_KHUAN],
  },

  {
    i18n: "danhMuc.danhMucDonViAxis",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/don-vi-axis",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DON_VI_AXIS],
  },
  {
    i18n: "danhMuc.danhMucDonViSph",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/don-vi-sph",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DON_VI_SPH],
  },
  {
    i18n: "danhMuc.danhMucTatKhucXa",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/tat-khuc-xa",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].TAT_KHUC_XA],
  },
  {
    i18n: "danhMuc.danhMucDonViCyl",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/don-vi-cyl",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DON_VI_CYL],
  },
  {
    i18n: "danhMuc.danhMucNhanAp",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/nhan-ap",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].NHAN_AP],
  },
  {
    i18n: "danhMuc.danhMucKhaiBaoPhuCapPttt",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/khai-bao-phu-cap-pttt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].KHAI_BAO_PHU_CAP_PTTT],
  },
  {
    i18n: "danhMuc.danhMucLuongGia",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/luong-gia-phcn",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [],
  },
  {
    i18n: "danhMuc.danhMucTuDienYKhoa",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/tu-dien-y-khoa",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].TU_DIEN_Y_KHOA],
  },
  {
    i18n: "danhMuc.danhMucKetQuaChanDoanLaoKhangThuoc",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/lao-khang-thuoc",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].LAO_KHANG_THUOC],
  },
  {
    i18n: "danhMuc.danhMucPhuongPhapChanDoan",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/phuong-phap-chan-doan",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_CHAN_DOAN],
  },
  {
    i18n: "danhMuc.danhMucDinhMucThuocVTYT",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/dinh-muc-thuoc-vtyt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DINH_MUC_THUOC_VTYT],
  },
  {
    i18n: "danhMuc.danhMucPhanLoaiTheoTienSuDieuTri",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/phan-loai-tien-su",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_TIEN_SU],
  },
  {
    i18n: "danhMuc.danhMucNguonKhac",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nguon-khac",
    accessRoles: [ROLES["DANH_MUC"].NGUON_KHAC],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNhomLoaiDoiTuong",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-loai-doi-tuong",
    accessRoles: [ROLES["DANH_MUC"].NHOM_LOAI_DOI_TUONG],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNhomLoaiBenhAn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-loai-benh-an",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].NHOM_LOAI_BENH_AN],
  },
  {
    i18n: "danhMuc.danhMucThangInBaoCaoKho",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/thang-bao-cao",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].THANG_BAO_CAO],
  },
  // {
  //   i18n: t("danhMuc.danhMucTaiSanKhac"),
  //   icon: require("assets/images/pagehome/icChucNang1.png"),
  //   link: "/danh-muc/chuc-nang",
  //   accessRoles: [ROLES["DANH_MUC"].TAI_SAN_KHAC],
  //   group: NHOM_DANH_MUC.CHUNG,
  // },
  // {
  //   title: "Danh mục nhãn áp",
  //   icon: require("assets/images/pagehome/icChucNang1.png"),
  //   link: "/danh-muc/chuc-nang",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  // {
  //   title: "Danh mục thị lực",
  //   icon: require("assets/images/pagehome/icChucNang2.png"),
  //   link: "/danh-muc/chuc-nang",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  // {
  //   title: "Danh mục đơn vị Axis",
  //   icon: require("assets/images/pagehome/icChucNang1.png"),
  //   link: "/danh-muc/chuc-nang",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  // {
  //   title: "Danh mục đơn vị CYL",
  //   icon: require("assets/images/pagehome/icChucNang2.png"),
  //   link: "/danh-muc/chuc-nang",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  // {
  //   title: "Danh mục đơn vị SPH",
  //   icon: require("assets/images/pagehome/icChucNang1.png"),
  //   link: "/danh-muc/chuc-nang",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  {
    i18n: "danhMuc.danhMucVanDeLienQuanDenThuoc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/van-de-lien-quan-den-thuoc",
    accessRoles: [ROLES["DANH_MUC"].VAN_DE_THUOC],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucXNSaoBenhAn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/xet-nghiem-sao-benh-an",
    accessRoles: [ROLES["DANH_MUC"].XN_SAO_BENH_AN],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucNgoiThai",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/ngoi-thai",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].NGOI_THAI],
  },
  {
    i18n: "danhMuc.danhMucCachThucDe",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/cach-thuc-de",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].CACH_THUC_DE],
  },
  {
    i18n: "danhMuc.danhMucBienPhapCamMau",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/bien-phap-cam-mau",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].BIEN_PHAP_CAM_MAU],
  },
  {
    i18n: "danhMuc.danhMucLyDoDenKham",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/ly-do-den-kham",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].LY_DO_DEN_KHAM],
  },

  {
    i18n: "danhMuc.danhMucChinhSachHoaHong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/chinh-sach-hoa-hong",
    group: NHOM_DANH_MUC.COM_TOOL,
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    i18n: "danhMuc.danhMucNhomDichVuHoaHong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/nhom-dich-vu-hoa-hong",
    group: NHOM_DANH_MUC.COM_TOOL,
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    i18n: "danhMuc.danhMucDoiTacHoaHong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    link: "/danh-muc/doi-tac-hoa-hong",
    group: NHOM_DANH_MUC.COM_TOOL,
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    i18n: "danhMuc.danhMucDichVuHoaHong",
    icon: require("assets/images/pagehome/bgThietLap.png"),
    group: NHOM_DANH_MUC.COM_TOOL,
    link: "/danh-muc/dich-vu-hoa-hong",
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    i18n: "danhMuc.danhMucGoTat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/go-tat",
    accessRoles: [ROLES["DANH_MUC"].GO_TAT],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucNhomDaiPhieuNhapXuat",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-dai-phieu-nhap-xuat",
    accessRoles: [ROLES["DANH_MUC"].NHOM_DAI_PHIEU_XUAT],
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucCoPhim",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/co-phim",
    accessRoles: [ROLES["DANH_MUC"].CO_PHIM],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucPhanTangNguyCoBN",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phan-tang-nguy-co",
    accessRoles: [ROLES["DANH_MUC"].PHAN_TANG_NGUY_CO],
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucCoCheThuChiNoiBo",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/phu-cap-dv-ky-thuat",
    accessRoles: [ROLES["DANH_MUC"].PHU_CAP_DV_KY_THUAT],
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucThanhPhanThamGiaHoiChan",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/tham-gia-hoi-chan",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].THAM_GIA_HOI_CHAN],
  },
  {
    i18n: "danhMuc.danhMucDieuTriKetHop",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dieu-tri-ket-hop",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DIEU_TRI_KET_HOP],
  },
  {
    i18n: "danhMuc.danhMucThietLapDieuKienChiDinh",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/dieu-kien-chi-dinh",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].DIEU_KIEN_CHI_DINH],
  },
  {
    i18n: "danhMuc.danhMucNhomKhoaEkipHuongPhuCapTheoYeuCau",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-huong-phu-cap-pttt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].NHOM_HUONG_PHU_CAP_PTTT],
  },
  {
    i18n: "danhMuc.danhMucNhomPhuCap",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/nhom-phu-cap-pt-tt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].NHOM_PHU_CAP_PT_TT],
  },
  {
    i18n: "danhMuc.danhMucLoaiHienThiPhieuIn",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/loai-hien-thi-phieu-in",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].LOAI_HIEN_THI_PHIEU_IN],
  },
  {
    i18n: "danhMuc.danhMucKhaiBaoHangHoaDungKemDvkt",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/danh-muc/khai-bao-hang-hoa-dung-kem-dvkt",
    group: NHOM_DANH_MUC.CHUNG,
    accessRoles: [ROLES["DANH_MUC"].KHAI_BAO_HANG_HOA_DUNG_KEM_DVKT],
  },
];

export const ListDanhMucXN = [
  {
    title: "Lấy mẫu bệnh phẩm",
    i18n: "xetNghiem.layMauBenhPham",
    icon: <SVG.IcLayMauBenhPham />,
    link: "/xet-nghiem/lay-mau",
    accessRoles: [ROLES["XET_NGHIEM"].MH_LAY_MAU],
  },
  {
    title: "Thực hiện Sinh hóa- Huyết học",
    i18n: "xetNghiem.thucHienSinhHoaHuyetHoc",
    icon: <SVG.IcThucHienSinhHoaHuyetHoc />,
    link: "/xet-nghiem/sinh-hoa-huyet-hoc",
    accessRoles: [ROLES["XET_NGHIEM"].XET_NGHIEM_HH],
  },
  {
    title: "Thực hiện Giải phẫu bệnh- Vi sinh",
    i18n: "xetNghiem.thucHienGiaiPhauBenhViSinh",
    icon: <SVG.IcThucHienGiaiPhauBenhViSinh />,
    link: "/xet-nghiem/giai-phau-benh-vi-ky-sinh",
    accessRoles: [ROLES["XET_NGHIEM"].XET_NGHIEM_GPB],
  },
  {
    title: "Thuốc, VTYT, hóa chất kèm xét nghiệm",
    i18n: "xetNghiem.thuocVTYTHoaChatKemXetNghiem",
    icon: <SVG.IcThucHienGiaiPhauBenhViSinh />,
    link: "/xet-nghiem/thuoc-vat-tu-hoa-chat",
    accessRoles: [ROLES["XET_NGHIEM"].MH_KE_DV_KEM_XET_NGHIEM],
  },
];

export const ListDanhMucTN = [
  {
    title: "Danh sách phiếu thu",
    i18n: "thuNgan.danhSachPhieuThu",
    icon: <SVG.IcDanhSachPhieuThu />,
    link: "/thu-ngan/danh-sach-phieu-thu",
    // accessRoles: [ROLES["THU_NGAN"].THU_NGAN],
  },
  {
    title: "Danh sách phiếu yêu cầu hoàn đổi",
    i18n: "thuNgan.danhSachPhieuYeuCauHoan",
    icon: <SVG.IcDanhSachPhieuYeuCauHoan />,
    link: "/thu-ngan/ds-phieu-yeu-cau-hoan",
    // accessRoles: [ROLES["THU_NGAN"].DANH_SACH_PHIEU_THU],
  },
  {
    title: "DS hóa đơn điện tử",
    i18n: "thuNgan.danhSachHoaDonDienTu",
    icon: <SVG.IcDanhSachHoaDonDienTu />,
    link: "/thu-ngan/ds-hoa-don-dien-tu",
    // accessRoles: [ROLES["THU_NGAN"].DANH_SACH_PHIEU_THU],
  },
  {
    title: "Quản lý tạm ứng",
    i18n: "thuNgan.quanLyTamUng.quanLyTamUng",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/quan-ly-tam-ung",
    accessRoles: [ROLES["THU_NGAN"].XEM_DS_NB_QUAN_LY_TAM_UNG],
  },
  {
    title: "Đồng bộ chứng từ kế toán",
    i18n: "thuNgan.dongBoChungTuKeToan",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/dong-bo-chung-tu-ke-toan",
    accessRoles: [ROLES["THU_NGAN"].DONG_BO_CHUNG_TU_KE_TOAN],
  },
  {
    title: "Thu ngân dịch vụ ngoài điều trị",
    i18n: "thuNgan.dichVuNgoaiDieuTri.title",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/ds-thu-dich-vu-ngoai-dieu-tri",
    accessRoles: [ROLES["THU_NGAN"].THU_NGAN_DICH_VU_NGOAI_DIEU_TRI],
  },
  {
    title: "Chốt số doanh thu",
    i18n: "thuNgan.chotSoDoanhThu",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/chot-so-doanh-thu",
    accessRoles: [ROLES["DANH_MUC"].CHOT_SO_DOANH_THU],
  },
  {
    title: "Danh sách người bệnh tổng kết thanh toán",
    i18n: "thuNgan.danhSachNguoiBenhTongKetThanhToan",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/danh-sach-nb-tong-ket-thanh-toan",
    accessRoles: [ROLES["THU_NGAN"].DANH_SACH_NGUOI_BENH_TONG_KET_THANH_TOAN],
  },
  {
    title: "Danh sách hoàn ngân hàng",
    i18n: "thuNgan.danhSachHoanNganHang",
    icon: <SVG.IcQuanLyTamUng />,
    link: "/thu-ngan/danh-sach-hoan-ngan-hang",
    accessRoles: [ROLES["THU_NGAN"].DANH_SACH_HOAN_NGAN_HANG],
  },
];

export const ListQuanTriHeThong = [
  {
    title: "Danh mục vai trò",
    i18n: "danhMuc.danhMucVaiTro",
    icon: <SVG.IcDanhMucVaiTro />,
    link: "/quan-tri/danh-muc-vai-tro",
    accessRoles: [ROLES["QUAN_LY_TAI_KHOAN"].VAI_TRO_HE_THONG],
  },
  {
    title: "Quản lý tài khoản",
    i18n: "danhMuc.quanLyTaiKhoan",
    icon: <SVG.IcQuanLyTaiKhoan />,
    link: "/quan-tri/danh-muc-tai-khoan",
    accessRoles: [ROLES["QUAN_LY_TAI_KHOAN"].QUAN_LY_TAI_KHOAN],
  },
  {
    title: "Danh mục Nhân viên",
    i18n: "danhMuc.danhMucNhanVien",
    icon: <SVG.IcDanhMucNhanVien />,
    link: "/quan-tri/nhan-vien",
    accessRoles: [ROLES["QUAN_LY_TAI_KHOAN"].NHAN_VIEN],
  },
  {
    title: "Danh mục quyền",
    i18n: "danhMuc.danhMucQuyen",
    icon: <SVG.IcDanhMucQuyen />,
    link: "/quan-tri/quyen",
    accessRoles: [ROLES["QUAN_LY_TAI_KHOAN"].QUYEN],
  },
  {
    title: "Danh mục nhóm tính năng",
    i18n: "danhMuc.danhMucNhomTinhNang",
    icon: <SVG.IcDanhMucNhomTinhNang />,
    link: "/quan-tri/nhom-tinh-nang",
    accessRoles: [ROLES["QUAN_LY_TAI_KHOAN"].NHOM_TINH_NANG],
  },
  {
    title: "Tuỳ chỉnh giao diện phần mềm",
    i18n: "common.tuyChinhGiaoDienPhanMem",
    icon: <SVG.IcTuyChinhGiaoDienPhanMem />,
    link: "/quan-tri/tuy-chinh-giao-dien-phan-mem",
    accessRoles: [ROLES["QUAN_TRI_HE_THONG"].XEM_TUY_CHINH_GIAO_DIEN_PHAN_MEM],
  },
  {
    title: "Danh mục mã tuỳ chỉnh",
    i18n: "danhMuc.danhMucMaTuyChinh",
    icon: <SVG.IcTuyChinhGiaoDienPhanMem />,
    link: "/quan-tri/danh-muc-ma-tuy-chinh",
    accessRoles: [ROLES["ADMIN"]],
  },
  {
    title: "Log người dùng",
    i18n: "danhMuc.logNguoiDung",
    icon: <SVG.IcTuyChinhGiaoDienPhanMem />,
    link: "/quan-tri/log-nguoi-dung",
    accessRoles: [ROLES["ADMIN"]],
  },
];

export const ListThietLap = [
  {
    title: "Thiết lập chung",
    i18n: "thietLap.thietLapChung",
    icon: <SVG.IcThietLapChung />,
    link: "/thiet-lap/thiet-lap-chung",
    accessRoles: [ROLES.THIET_LAP_CHUNG],
  },
  {
    title: "Tách gộp phiếu Xét nghiệm",
    i18n: "thietLap.thietLapTachGopPhieuChiDinhXetNghiem",
    icon: <SVG.IcTachGopPhieuXetNghiem />,
    link: "/thiet-lap/tach-gop-phieu-xet-nghiem",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_TACH_GOP_PHIEU_XET_NGHIEM],
  },
  {
    title: "Tách gộp phiếu chỉ định DVKT",
    i18n: "thietLap.thietLapTachGopChiDinhDvCDHA_TDCN_PTTT",
    icon: <SVG.IcTachGopPhieuChiDinhDvkt />,
    link: "/thiet-lap/tach-gop-phieu-dvkt",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_TACH_GOP_PHIEU_DKVT],
  },
  {
    title: "Thiết lập tích điểm",
    i18n: "thietLap.thietLapTichDiem",
    icon: <SVG.IcThietLapTichDiem />,
    link: "/thiet-lap/tich-diem",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_TICH_DIEM],
  },
  {
    title: "Thiết lập thông số hàng đợi",
    i18n: "thietLap.thietLapThongSoHangDoi",
    icon: <SVG.IcThietLapThongSoHangDoi />,
    link: "/thiet-lap/thong-so-hang-doi",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_THONG_SO_HANG_DOI],
  },
  {
    title: "Thiết lập phiếu tại các màn hình",
    i18n: "thietLap.thietLapPhieuTaiCacManHinh",
    icon: <SVG.IcThietLapPhieuTaiCacManHinh />,
    link: "/thiet-lap/thiet-lap-phieu-in",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_PHIEU_IN],
  },
  {
    title: "Thiết lập phiếu lĩnh trả",
    i18n: "thietLap.thietLapPhieuLinhTra",
    icon: <SVG.IcThietLapPhieuLinhTra />,
    link: "/thiet-lap/phieu-linh-tra",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_PHIEU_LINH_TRA],
  },
  {
    title: "Thiết lập chọn giường",
    i18n: "danhMuc.thietLapChonGiuong",
    icon: <SVG.IcThietLapChonGiuong />,
    link: "/thiet-lap/thiet-lap-chon-giuong",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_CHON_GIUONG],
  },
  {
    title: "Thiết lập nhúng link",
    i18n: "thietLap.thietLapNhungLink",
    icon: <SVG.IcThietLapNhungLink />,
    link: "/thiet-lap/thiet-lap-nhung-link",
    accessRoles: [ROLES["DANH_MUC"].POWERBI],
  },
  {
    title: "Thiết lập điều kiện chuyển khoa ra viện",
    i18n: "thietLap.thietLapDieuKienChuyenKhoaRaVien",
    icon: <SVG.IcThietLapDieuKienChuyenKhoaRaVien />,
    link: "/thiet-lap/thiet-lap-dieu-kien-chuyen-khoa-ra-vien",
    accessRoles: [ROLES["DANH_MUC"].DIEU_KIEN_CHUYEN_KHOA_RA_VIEN],
  },
  {
    title: "Thiết lập lưu trữ bệnh án",
    i18n: "thietLap.thietLapLuuTruBenhAn",
    icon: <SVG.IcThietLapLuuTruBenhAn />,
    link: "/thiet-lap/luu-tru-benh-an",
    accessRoles: [ROLES["THIET_LAP"].XEM_LUU_TRU_BA],
  },
  {
    title: "Thiết lập tự động tất toán",
    i18n: "thietLap.thietLapTuDongTatToan",
    icon: <SVG.IcThietLapLuuTruBenhAn />,
    link: "/thiet-lap/thiet-lap-tu-dong-tat-toan",
    accessRoles: [ROLES["THIET_LAP"].XEM_TU_DONG_TAT_TOAN],
  },
  {
    title: "Thiết lập Đổi mã người bệnh",
    i18n: "thietLap.thietLapDoiMaNguoiBenh",
    icon: <SVG.IcThietLapDoiMaNguoiBenh />,
    link: "/thiet-lap/doi-ma-nguoi-benh",
    accessRoles: [ROLES["THIET_LAP"].XEM_DOI_MA_NB],
  },
  {
    title: "Thiết lập Đổi mã hồ sơ",
    i18n: "thietLap.thietLapDoiMaHoSo",
    icon: <SVG.IcThietLapDoiMaNguoiBenh />,
    link: "/thiet-lap/doi-ma-ho-so",
    accessRoles: [ROLES["THIET_LAP"].XEM_DOI_MA_HO_SO],
  },
  {
    title: "Thiết lập số liên in",
    i18n: "thietLap.thietLapSoLienIn",
    icon: <SVG.IcThietLapSoLienIn />,
    link: "/thiet-lap/lien-in",
    accessRoles: [ROLES["DANH_MUC"].LIEN_IN],
  },
  {
    title: "Thiết lập giá trị chỉ số sống",
    i18n: "thietLap.thietLapGiaTriCSS",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/thiet-lap-gia-tri-css",
    accessRoles: [ROLES["DANH_MUC"].THIET_LAP_GIA_TRI_CSS],
  },
  {
    title: "Thiết lập Nhóm dịch vụ báo cáo",
    i18n: "thietLap.thietLapNhomDvBaoCao",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/nhom-dv-bao-cao",
    accessRoles: [],
  },
  {
    title: "Thiết lập hướng điều trị",
    i18n: "thietLap.thietLapHuongDieuTri",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/huong-dieu-tri",
    accessRoles: [ROLES["THIET_LAP"].HUONG_DIEU_TRI],
  },
  {
    title: "Thiết lập phòng thực hiện",
    i18n: "thietLap.thietLapPhongThucHien",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/thiet-lap-phong-thuc-hien",
    accessRoles: [ROLES["THIET_LAP"].THIET_LAP_PHONG_THUC_HIEN],
  },
  {
    title: "Thiết lập đóng mở phòng thực hiện /DV CĐHA-TDCN",
    i18n: "thietLap.thietLapDongMoPhongThucHienDvCdha",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/thiet-lap-dong-mo-phong-thuc-hien-va-dv-cdha-tdcn",
    accessRoles: [ROLES["THIET_LAP"].THIET_LAP_DONG_MO_PHONG_THUC_HIEN],
  },
  {
    title: "Thiết lập loại dịch vụ - nhóm chi phí",
    i18n: "thietLap.thietLapLoaiDichVuNhomChiPhi",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/thiet-lap-loai-dich-vu-nhom-chi-phi",
    accessRoles: [ROLES["THIET_LAP"].THIET_LAP_LOAI_DICH_VU_NHOM_CHI_PHI],
  },
  {
    title: "Thiết lập sinh số thứ tự thu ngân",
    i18n: "thietLap.thietLapSinhSoThuTuThuNgan",
    icon: <SVG.IcThietLapGiaTriCSS />,
    link: "/thiet-lap/thiet-lap-sinh-so-thu-tu-thu-ngan",
    accessRoles: [ROLES["THIET_LAP"].XEM_THIET_LAP_SINH_SO_THU_TU_THU_NGAN],
  },
];

export const ListKhoMau = [
  {
    i18n: "khoMau.nhapKhoMau",
    icon: <SVG.IcNhapKhoMau />,
    link: "/kho-mau/nhap-kho",
    accessRoles: [ROLES["KHO_MAU"].XEM_DANH_SACH_NHAP_KHO_MAU],
  },
  {
    i18n: "khoMau.danhSachTonKhoMau",
    icon: <SVG.IcDanhSachTonKhoMau />,
    link: "/kho-mau/danh-sach-ton-kho",
    accessRoles: [ROLES["KHO_MAU"].XEM_DANH_SACH_TON_KHO_MAU],
  },
  {
    i18n: "khoMau.truyenPhatMau",
    icon: <SVG.IcTruyenPhatMau />,
    link: "/kho-mau/truyen-phat-mau",
    accessRoles: [ROLES["KHO_MAU"].XEM_DANH_SACH_TRUYEN_PHAT_MAU],
  },
  {
    i18n: "khoMau.xuatKhoMau",
    icon: <SVG.IcXuatKhoMau />,
    link: "/kho-mau/xuat-kho",
    accessRoles: [ROLES["KHO_MAU"].XEM_DANH_SACH_XUAT_KHO_MAU],
  },
  {
    i18n: "khoMau.danhSachTraMau",
    icon: <SVG.IcXuatKhoMau />,
    link: "/kho-mau/tra-mau",
    accessRoles: [ROLES["KHO_MAU"].XEM_DANH_SACH_TRA_MAU],
  },
];

export const ListKho = [
  {
    i18n: "kho.thietLapKhoChiDinh",
    icon: <SVG.IcThietLapKhoChiDinh />,
    link: "/kho/thiet-lap-kho-chi-dinh",
    accessRoles: [ROLES["KHO"].XEM_THIET_LAP_KHO_CHI_DINH],
  },
  {
    i18n: "kho.quanTriKho",
    icon: <SVG.IcQuanTriKho />,
    link: "/kho/quan-tri-kho",
    accessRoles: [ROLES["KHO"].XEM_QUAN_TRI_KHO],
  },
  {
    i18n: "kho.quanLyThau",
    icon: <SVG.IcQuanLyThau />,
    link: "/kho/quan-ly-thau",
    accessRoles: [ROLES["KHO"].XEM_MAN_HINH_QUAN_LY_THAU],
  },
  {
    i18n: "kho.nhapKho",
    icon: <SVG.IcNhapKho />,
    link: "/kho/nhap-kho",
  },
  {
    i18n: "kho.xuatKho",
    icon: <SVG.IcXuatKho />,
    link: "/kho/xuat-kho",
    accessRoles: [ROLES["KHO"].XEM_PHIEU_XUAT_KHO],
  },
  {
    i18n: "kho.danhSachTonKho",
    icon: <SVG.IcDanhSachTonKho />,
    link: "/kho/danh-sach-ton-kho",
  },
  {
    i18n: "kho.phatThuoc.phatThuocNgoaiTruRaVien",
    icon: <SVG.IcPhatThuocNgoaiTruRaVien />,
    link: "/kho/phat-thuoc-ngoai-tru",
  },
  {
    i18n: "kho.vatTuKyGui",
    icon: <SVG.IcVatTuKyGui />,
    link: "/kho/vat-tu-ky-gui",
    accessRoles: [ROLES["KHO"].XEM_MAN_HINH_VAT_TU_KY_GUI],
  },
  {
    i18n: "kho.danhSachNbSuDungThuoc",
    icon: <SVG.IcDanhSachTonKho />,
    link: "/kho/danh-sach-nb-su-dung-thuoc",
    accessRoles: [ROLES["KHO"].XEM_DS_NB_SU_DUNG_THUOC],
  },
  {
    i18n: "kho.danhSachDuyetDuocLamSang",
    icon: <SVG.IcDanhSachDuyetDuocLamSang />,
    link: "/kho/danh-sach-duyet-duoc-lam-sang",
    accessRoles: [ROLES["KHO"].DUYET_DUOC_LAM_SANG],
  },
  {
    i18n: "kho.dsPhieuTuVanThuoc",
    icon: <SVG.IcDSTuVanThuoc />,
    link: "/kho/ds-phieu-tu-van-thuoc",
    accessRoles: [ROLES["KHO"].XEM_DS_TU_VAN_THUOC],
  },
  {
    i18n: "kho.danhSachHangHoaChoTra",
    icon: <SVG.IcDanhSachNguoiBenhChuaHoanThanhTraHangHoa />,
    link: "/kho/danh-sach-hang-hoa-cho-tra",
    accessRoles: [ROLES["KHO"].XEM_DS_HANG_HOA_CHO_TRA],
  },
  {
    i18n: "kho.danhSachHangHoaChoLinh",
    icon: <SVG.IcDanhSachNguoiBenhChuaHoanThanhTraHangHoa />,
    link: "/kho/danh-sach-hang-hoa-cho-linh",
    accessRoles: [ROLES["KHO"].XEM_DS_HANG_HOA_CHO_LINH],
  },
  {
    i18n: "kho.danhSachBanGiaoThuocDungCuThuongTruc",
    icon: <SVG.IcDanhSachNguoiBenhChuaHoanThanhTraHangHoa />,
    link: "/kho/danh-sach-ban-giao-thuoc-dung-cu-thuong-truc",
    accessRoles: [ROLES["KHO"].XEM_DS_BAN_GIAO_THUOC_DUNG_CU_THUONG_TRUC],
  },
];

export const ListChanDoanHinhAnh = [
  {
    title: "Chờ tiếp đón",
    i18n: "cdha.choTiepDon",
    icon: <SVG.IcChoTiepDon />,
    link: "/chan-doan-hinh-anh/cho-tiep-don",
    accessRoles: [ROLES["CHAN_DOAN_HINH_ANH"].CHO_TIEP_DON],
  },
  {
    title: "Tiếp nhận",
    i18n: "common.tiepNhan",
    icon: <SVG.IcTiepNhan1 />,
    link: "/chan-doan-hinh-anh/thuc-hien-cdha-tdcn",
    accessRoles: [ROLES["CHAN_DOAN_HINH_ANH"].TIEP_NHAN],
  },
  {
    title: "Biểu đồ thống kê",
    i18n: "cdha.bieuDoThongKe",
    icon: <SVG.IcBieuDoThongKe />,
    link: "/chan-doan-hinh-anh/bieu-do-thong-ke",
  },
];

export const ListHoSoBenhAn = [
  {
    title: "Danh sách người bệnh",
    i18n: "common.danhSachNguoiBenh",
    icon: <SVG.IcHoSoBenhAn />,
    link: "/ho-so-benh-an/danh-sach-nguoi-benh",
  },
];

export const ListPhieuTheoDoiDieuTri = [
  {
    title: "Danh sách người bệnh cần theo dõi",
    i18n: "theoDoiDieuTri.danhSachNguoiBenhCanTheoDoi",
    icon: <SVG.IcTheoDoiDieuTri />,
    link: "/theo-doi-nguoi-benh/danh-sach-nguoi-benh",
    accessRoles: [ROLES["THEO_DOI_NGUOI_BENH"].XEM_DS_NB_CAN_THEO_DOI],
  },
  {
    title: "Danh sách người bệnh theo dõi kết quả",
    i18n: "theoDoiDieuTri.danhSachNguoiBenhTheoDoiKetQua",
    icon: <SVG.IcTheoDoiDieuTri />,
    link: "/theo-doi-nguoi-benh/danh-sach-nguoi-benh-theo-doi-ket-qua",
    accessRoles: [ROLES["THEO_DOI_NGUOI_BENH"].XEM_DS_NB_THEO_DOI_KET_QUA],
  },
  {
    title: "Danh sách người bệnh chờ kết luận khám",
    i18n: "theoDoiDieuTri.danhSachNguoiBenhChoKetLuanKham",
    icon: <SVG.IcTheoDoiDieuTri />,
    link: "/theo-doi-nguoi-benh/danh-sach-nguoi-benh-cho-ket-luan-kham",
    accessRoles: [ROLES["THEO_DOI_NGUOI_BENH"].XEM_DS_NB_CHO_KET_LUAN_KHAM],
  },
];

export const ListQuanLyDinhDuong = [
  {
    title: "Danh sách phiếu lĩnh suất ăn",
    i18n: "quanLyNoiTru.danhSachPhieuLinhSuatAn",
    icon: <SVG.IcDanhSachPhieuLinhSuatAn />,
    link: "/quan-ly-dinh-duong/danh-sach-phieu-linh-suat-an",
    accessRoles: [ROLES["QUAN_LY_DINH_DUONG"].HIEN_THI_DS_PHIEU_LINH_SUAT_AN],
  },
  {
    title: "Danh sách phiếu trả suất ăn",
    i18n: "quanLyNoiTru.danhSachPhieuTraSuatAn",
    icon: <SVG.IcDanhSachPhieuTraSuatAn />,
    link: "/quan-ly-dinh-duong/danh-sach-phieu-tra-suat-an",
    accessRoles: [ROLES["QUAN_LY_DINH_DUONG"].HIEN_THI_DS_PHIEU_TRA_SUAT_AN],
  },
  {
    title: "Danh sách NB quản lý dinh dưỡng",
    i18n: "quanLyDinhDuong.danhSachNbQuanLyDinhDuong",
    icon: <SVG.IcDanhSachNbQuanLyDinhDuong />,
    link: "/quan-ly-dinh-duong/ds-nb-quan-ly-dinh-duong",
  },
  {
    i18n: "kho.danhSachSuatAnChuaLinhDuyetTra",
    icon: <SVG.IcDanhSachSuatAnChuaLinhDuyetTra />,
    link: "/quan-ly-dinh-duong/danh-sach-suat-an-chua-linh-duyet-tra",
    accessRoles: [ROLES["KHO"].XEM_DS_SUAT_AN_CHUA_LINH_DUYET_TRA],
  },
  {
    title: "Khai báo chỉ số đánh giá dinh dưỡng trẻ em",
    i18n: "quanLyDinhDuong.khaiBaoChiSoDanhGiaDinhDuongTreEm",
    icon: <SVG.IcKhaiBaoChiSoDinhDuongTreEm />,
    link: "/quan-ly-dinh-duong/chi-so-danh-gia-dinh-duong-tre-em",
  },
];

export const ListPhaCheThuoc = [
  {
    title: "Khai báo công thức pha chế",
    i18n: "phaCheThuoc.khaiBaoCongThucPhaChe",
    icon: <SVG.IcKhaiBaoCongThucPhaChe />,
    link: "/pha-che-thuoc/cong-thuc-pha-che",
    accessRoles: [ROLES["PHA_CHE_THUOC"].CONG_THUC_PHA_CHE],
  },
  {
    title: "Danh sách phiếu pha chế thuốc",
    i18n: "phaCheThuoc.danhSachPhieuPhaCheThuoc",
    icon: <SVG.IcDanhSachPhieuPhaCheThuoc />,
    link: "/pha-che-thuoc/danh-sach-pha-che-thuoc",
    accessRoles: [ROLES["PHA_CHE_THUOC"].DANH_SACH_PHA_CHE],
  },
  {
    title: "Danh sách phiếu xuất pha chế thuốc",
    i18n: "phaCheThuoc.danhSachPhieuXuatPhaCheThuoc",
    icon: <SVG.IcDanhSachPhieuXuatPhaCheThuoc />,
    link: "/pha-che-thuoc/danh-sach-phieu-xuat-pha-che-thuoc",
    accessRoles: [
      ROLES["PHA_CHE_THUOC"].DANH_SACH_PHIEU_XUAT_PHA_CHE,
      ROLES["PHA_CHE_THUOC"].XEM_CHI_TIET_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].SUA_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].XOA_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_KHO,
      ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_PHA_CHE,
      ROLES["PHA_CHE_THUOC"].IN_TEM_SAN_PHAM_PHA_CHE_HANG_LOAT,
      ROLES["PHA_CHE_THUOC"].HOAN_THANH,
      ROLES["PHA_CHE_THUOC"].HUY_HOAN_THANH,
      ROLES["PHA_CHE_THUOC"].THEM_PHIEU_XUAT_PHA_CHE,
    ],
  },
  {
    title: "Danh sách phiếu xuất pha chế thuốc theo đơn",
    i18n: "phaCheThuoc.danhSachPhieuPhaCheThuocTheoDon",
    icon: <SVG.IcDanhSachPhieuPhaCheThuoc />,
    link: "/pha-che-thuoc/danh-sach-pha-che-thuoc-theo-don",
    accessRoles: [],
  },
  {
    title: "Danh sách phiếu chốt pha chế",
    i18n: "phaCheThuoc.danhSachPhieuChotPhaChe",
    icon: <SVG.IcDanhSachPhieuPhaCheThuoc />,
    link: "/pha-che-thuoc/danh-sach-phieu-chot-pha-che",
    accessRoles: [],
  },
];

export const ListKySo = [
  {
    title: "Thiết lập quyền ký",
    i18n: "kySo.thietLapQuyenKy",
    icon: <SVG.IcThietLapQuyenKy />,
    link: "/ky-so/thiet-lap-quyen-ky",
    accessRoles: [ROLES["KY_SO"].HIEN_THI_THIET_LAP_QUYEN_KY],
  },
  {
    title: "Danh sách phiếu ký",
    i18n: "kySo.danhSachPhieuKy",
    icon: <SVG.IcDanhSachPhieuChoKy />,
    link: "/ky-so/danh-sach-phieu-ky",
  },
];

export const ListQuyetToanBHYT = [
  {
    title: "Danh sách người bệnh chờ tạo hồ sơ quyết toán BHYT",
    i18n: "quyetToanBhyt.danhSachNguoiBenhChoTaoHoSoQuyetToanBhyt",
    icon: <SVG.IcDanhSachNbChoTaoHoSoQtBhyt />,
    link: "/quyet-toan-bhyt/danh-sach-nguoi-benh-cho-tao-ho-so-quyet-toan-bhyt?dsDoiTuong=2",
    accessRoles: [
      ROLES["QUYET_TOAN_BHYT"].XEM_DANH_SACH_NGUOI_BENH_CHO_TAO_QUYET_TOAN_BHYT,
    ],
  },
  {
    title: "Danh sách hồ sơ bảo hiểm (79A 46 cột theo QĐ4210)",
    i18n: "quyetToanBhyt.danhSachHoSoHiemHiem4210",
    icon: <SVG.IcDanhSachHoSoBaoHiemTheoQD4210 />,
    link: "/quyet-toan-bhyt/danh-sach-ho-so-bao-hiem-79a-46cot-theo-qd4210?dsTrangThai=10",
    accessRoles: [
      ROLES["QUYET_TOAN_BHYT"]
        .XEM_DANH_SACH_HO_SO_BAO_HIEM_79A_46COT_THEO_QD4210,
    ],
  },
  {
    title: "Danh sách hồ sơ bảo hiểm (Theo QĐ130)",
    i18n: "quyetToanBhyt.danhSachHoSoBaoHiemTheoQd130",
    icon: <SVG.IcDanhSachHoSoBaoHiemTheoQD130 />,
    link: "/quyet-toan-bhyt/danh-sach-ho-so-bao-hiem-theo-qd130?dsTrangThai=10",
    accessRoles: [ROLES["QUYET_TOAN_BHYT"].XEM_DANH_SACH_HO_SO_BAO_HIEM_QD_130],
  },
  {
    title: "Danh sách hồ sơ dịch vụ (Theo QĐ130)",
    i18n: "quyetToanBhyt.danhSachHoSoDichVuTheoQd130",
    icon: <SVG.IcDanhSachHoSoDichVuTheoQD130 />,
    link: "/quyet-toan-bhyt/danh-sach-ho-so-dich-vu-theo-qd130?dsTrangThai=10",
    accessRoles: [ROLES["QUYET_TOAN_BHYT"].XEM_DANH_SACH_HO_SO_DICH_VU_QD_130],
  },
  {
    title: "Danh sách hồ sơ bảo hiểm đã xoá",
    i18n: "quyetToanBhyt.danhSachHoSoBaoHiemDaXoa",
    icon: <SVG.IcDanhSachHoSoBaoHiemDaXoa />,
    link: "/quyet-toan-bhyt/danh-sach-ho-so-bao-hiem-da-xoa",
    accessRoles: [ROLES["QUYET_TOAN_BHYT"].DANH_SACH_HO_SO_BAO_HIEM_BI_XOA],
  },
];

export const ListNhomDanhMuc = [
  // {
  //   title: "Danh mục chuyên khoa mắt",
  //   group: NHOM_DANH_MUC.CHUYEN_KHOA_MAT,
  // },
  {
    i18n: "danhMuc.danhMucVeCapCuu",
    group: NHOM_DANH_MUC.CAP_CUU,
  },
  {
    i18n: "danhMuc.danhMucVeKho",
    group: NHOM_DANH_MUC.KHO,
  },
  {
    i18n: "danhMuc.danhMucVeKyInPhieu",
    group: NHOM_DANH_MUC.KY_IN_PHIEU,
  },
  {
    i18n: "danhMuc.danhMucVeDichVu",
    group: NHOM_DANH_MUC.DICH_VU,
  },
  {
    i18n: "danhMuc.danhMucVeTTHanhChinh",
    group: NHOM_DANH_MUC.HANH_CHINH,
  },
  {
    i18n: "danhMuc.danhMucChung",
    group: NHOM_DANH_MUC.CHUNG,
  },
  {
    i18n: "danhMuc.danhMucChamSocKhachHang",
    group: NHOM_DANH_MUC.KHACH_HANG,
  },
  {
    i18n: "title.quanLyHoaHong",
    group: NHOM_DANH_MUC.COM_TOOL,
  },
];

export const ListNhomBaoCao = [
  {
    i18n: "baoCao.baoCaoTaiChinh",
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
  },
  {
    i18n: "baoCao.baoCaoDichVu",
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
  },
  {
    i18n: "baoCao.baoCaoPhongKham",
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
  },
  {
    i18n: "baoCao.baoCaoKho",
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
  },
  {
    i18n: "baoCao.baoCaoKhoVatTuHoaChat",
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
  },
  {
    i18n: "baoCao.baoCaoNhaThuoc",
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
  },
  {
    i18n: "baoCao.baoCaoSucKhoe",
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
  },
  {
    i18n: "baoCao.baoCaoGoiLieuTrinh",
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
  },
  {
    i18n: "baoCao.baoCaoKeHoachTongHop",
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
  },
  {
    i18n: "baoCao.baoCaoPhauThuatThuThuat",
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
  },
  {
    i18n: "baoCao.baoCaoSuatAn",
    group: NHOM_BAO_CAO.BAO_CAO_SUAT_AN,
  },
  {
    i18n: "baoCao.baoCaoTiemChung",
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
  },
  {
    i18n: "baoCao.baoCaoKsnk",
    group: NHOM_BAO_CAO.BAO_CAO_KSNK,
  },
  {
    i18n: "baoCao.baoCaoLao",
    group: NHOM_BAO_CAO.BAO_CAO_LAO,
  },
  {
    i18n: "baoCao.baoCaoKhoDinhDuong",
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
  },
  {
    i18n: "baoCao.baoCaoQuanTri",
    group: NHOM_BAO_CAO.BAO_CAO_QT,
  },
  {
    i18n: "baoCao.baoCaoDuyetDuocLamSang",
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
  },
  {
    i18n: "baoCao.baoCaoQuyetToanBaoHiem",
    group: NHOM_BAO_CAO.BAO_CAO_QUYET_TOAN_BAO_HIEM,
  },
  {
    i18n: "baoCao.baoCaoSangLocDinhDuong",
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
  },
  {
    i18n: "baoCao.baoCaoPhaCheThuoc",
    group: NHOM_BAO_CAO.BAO_CAO_PHA_CHE_THUOC,
  },
];
export const ListBaoCao = [
  {
    title: "PK01. Danh sách người bệnh khám chi tiết",
    i18n: "baoCao.pk01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/danh-sach-nguoi-benh-kham-chi-tiet",
    accessRoles: [ROLES["BAO_CAO"].NGUOI_BENH_KHAM_CHI_TIET],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK02. Danh sách người bệnh có lịch hẹn khám",
    i18n: "baoCao.pk02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/danh-sach-nguoi-benh-co-lich-hen-kham",
    accessRoles: [ROLES["BAO_CAO"].NGUOI_BENH_CO_LICH_HEN_KHAM],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK03. Danh sách người bệnh bảo hiểm y tế",
    i18n: "baoCao.pk03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-03",
    accessRoles: [ROLES["BAO_CAO"].PK03],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK04. Danh sách người bệnh và lịch khám",
    i18n: "baoCao.pk04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-04",
    accessRoles: [ROLES["BAO_CAO"].PK04],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK06. Danh sách người bệnh cần khám chuyên sâu",
    i18n: "baoCao.pk06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-06",
    accessRoles: [ROLES["BAO_CAO"].PK06],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK07. Danh sách người bệnh cần theo dõi",
    i18n: "baoCao.pk07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-07",
    accessRoles: [ROLES["BAO_CAO"].PK07],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK08. Báo cáo số lượt khám bệnh của NB đăng ký khám",
    i18n: "baoCao.pk08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-08",
    accessRoles: [ROLES["BAO_CAO"].PK08],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK09. Danh sách người bệnh khám theo chẩn đoán chi tiết",
    i18n: "baoCao.pk09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-09",
    accessRoles: [ROLES["BAO_CAO"].PK09],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK10. Báo cáo Thống kê sl nb theo ngày",
    i18n: "baoCao.pk10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-10",
    accessRoles: [ROLES["BAO_CAO"].PK10],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK11. Báo cáo giao ban khoa khám bệnh",
    i18n: "baoCao.pk11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-11",
    accessRoles: [ROLES["BAO_CAO"].PK11],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK12. Báo cáo hằng ngày theo sổ giao ban",
    i18n: "baoCao.pk12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-12",
    accessRoles: [ROLES["BAO_CAO"].PK12],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK13. Báo cáo tổng hợp số lượng theo bác sĩ khám",
    i18n: "baoCao.pk13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-13",
    accessRoles: [ROLES["BAO_CAO"].PK13],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK14. Báo cáo Sổ khám bệnh",
    i18n: "baoCao.pk14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-14",
    accessRoles: [ROLES["BAO_CAO"].PK14],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "PK15. Báo cáo khách hàng",
    i18n: "baoCao.pk15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pk-15",
    accessRoles: [ROLES["BAO_CAO"].PK15],
    group: NHOM_BAO_CAO.BAO_CAO_PHONG_KHAM,
    capitalizeTitle: false,
  },
  {
    title: "BC01. Báo cáo chi tiết theo người bệnh",
    i18n: "baoCao.bc01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-01",
    accessRoles: [ROLES["BAO_CAO"].BC01],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC02. Báo cáo chi tiết người bệnh đã tiếp đón",
    i18n: "baoCao.bc02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/chi-tiet-nguoi-benh-da-tiep-don",
    accessRoles: [ROLES["BAO_CAO"].BC02],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.bc02_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-02_1",
    accessRoles: [ROLES["BAO_CAO"].BC02_1],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC03. Thống kê số lượng người bệnh theo loại dịch vụ",
    i18n: "baoCao.bc03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-03",
    accessRoles: [ROLES["BAO_CAO"].BC03],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC04. Thống kê số lượng dịch vụ theo đối tượng",
    i18n: "baoCao.bc04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-04",
    accessRoles: [ROLES["BAO_CAO"].BC04],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC05. Báo cáo chi tiết tiếp nhận dịch vụ theo phòng",
    i18n: "baoCao.bc05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-05",
    accessRoles: [ROLES["BAO_CAO"].BC05],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC06. Báo cáo chấm công thực hiện dịch vụ",
    i18n: "baoCao.bc06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-06",
    accessRoles: [ROLES["BAO_CAO"].BC06],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC07. Báo cáo chấm công thực hiện dịch vụ yêu cầu",
    i18n: "baoCao.bc07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-07",
    accessRoles: [ROLES["BAO_CAO"].BC07],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC08. Báo cáo số lượng dịch vụ từ bệnh viện E",
    i18n: "baoCao.bc08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-08",
    accessRoles: [ROLES["BAO_CAO"].BC08],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC09. Báo cáo thống kê dịch vụ theo NB",
    i18n: "baoCao.bc09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-09",
    accessRoles: [ROLES["BAO_CAO"].BC09],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC10. Báo cáo chi tiết dịch vụ",
    i18n: "baoCao.bc10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-10",
    accessRoles: [ROLES["BAO_CAO"].BC10],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC10.1. Báo cáo chi tiết dịch vụ",
    i18n: "baoCao.bc10_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-10_1",
    accessRoles: [ROLES["BAO_CAO"].BC10_1],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC10.2. Báo cáo chi tiết dịch vụ theo khoa",
    i18n: "baoCao.bc10_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-10_2",
    accessRoles: [ROLES["BAO_CAO"].BC10_2],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC10.3 Báo cáo chi tiết dịch vụ theo ngày",
    i18n: "baoCao.bc10_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-10_3",
    accessRoles: [ROLES["BAO_CAO"].BC10_3],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC11. Báo cáo tổng hợp dịch vụ",
    i18n: "baoCao.bc11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-11",
    accessRoles: [ROLES["BAO_CAO"].BC11],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC11.1 Báo cáo tổng hợp dịch vụ CLS",
    i18n: "baoCao.bc11_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-11_1",
    accessRoles: [ROLES["BAO_CAO"].BC11_1],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC11.2 Báo cáo thống kê số lượng dịch vụ theo bác sĩ",
    i18n: "baoCao.bc11_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-11_2",
    accessRoles: [ROLES["BAO_CAO"].BC11_2],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC12. Báo cáo tổng hợp bộ chỉ định đã sử dụng",
    i18n: "baoCao.bc12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-12",
    accessRoles: [ROLES["BAO_CAO"].BC12],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC14. Bảng chấm công thủ thuật được phụ cấp",
    i18n: "baoCao.bc14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-14",
    accessRoles: [ROLES["BAO_CAO"].BC14],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.bc14_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-14_1",
    accessRoles: [ROLES["BAO_CAO"].BC14_1],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC14.2 Bảng tổng hợp chấm công PT/TT (144-BYT/GĐ)",
    i18n: "baoCao.bc14_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-14_2",
    accessRoles: [ROLES["BAO_CAO"].BC14_2],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC15. Bảng chấm công ca mổ được phụ cấp",
    i18n: "baoCao.bc15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-15",
    accessRoles: [ROLES["BAO_CAO"].BC15],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC16. Báo cáo Phẫu thuật - thủ thuật, CDHA - TDCN",
    i18n: "baoCao.bc16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-16",
    accessRoles: [ROLES["BAO_CAO"].BC16],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC17. Tổng hợp lĩnh máu theo tháng",
    i18n: "baoCao.bc17",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-17",
    accessRoles: [ROLES["BAO_CAO"].BC17],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC18. Báo cáo truyền máu theo tháng",
    i18n: "baoCao.bc18",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-18",
    accessRoles: [ROLES["BAO_CAO"].BC18],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC19. Báo cáo thủ thuật khoa PHCN",
    i18n: "baoCao.bc19",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-19",
    accessRoles: [ROLES["BAO_CAO"].BC19],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC20. Báo cáo chi tiết DV nội trú - DV theo đầu BN",
    i18n: "baoCao.bc20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-20",
    accessRoles: [ROLES["BAO_CAO"].BC20],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC21. Báo cáo thống kê suất ăn",
    i18n: "baoCao.bc21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-21",
    accessRoles: [ROLES["BAO_CAO"].BC21],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC22. Báo cáo công khai thuốc, XN, cận lâm sàng, y dụng cụ",
    i18n: "baoCao.bc22",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-22",
    accessRoles: [ROLES["BAO_CAO"].BC22],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC23. Thống kê thanh toán theo ca làm việc",
    i18n: "baoCao.bc23",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-23",
    accessRoles: [ROLES["BAO_CAO"].BC23],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC24. Báo cáo thống kê khám bệnh",
    i18n: "baoCao.bc24",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-24",
    accessRoles: [ROLES["BAO_CAO"].BC24],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC25. Báo cáo thống kê số lượng dịch vụ theo phòng thực hiện",
    i18n: "baoCao.bc25",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-25",
    accessRoles: [ROLES["BAO_CAO"].BC25],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.bc26",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-26",
    accessRoles: [ROLES["BAO_CAO"].BC26],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.bc27",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-27",
    accessRoles: [ROLES["BAO_CAO"].BC27],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC28. Báo cáo chi tiết người bệnh sử dụng bộ chỉ định",
    i18n: "baoCao.bc28",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-28",
    accessRoles: [ROLES["BAO_CAO"].BC28],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC29. Báo cáo số liệu tình hình khám chữa bệnh",
    i18n: "baoCao.bc29",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-29",
    accessRoles: [ROLES["BAO_CAO"].BC29],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC29.2. Thống kê số lượng NB khám xét nghiệm theo bác sĩ",
    i18n: "baoCao.bc29_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-29_2",
    accessRoles: [ROLES["BAO_CAO"].BC29_2],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC30. Báo cáo tổng hợp xuất truyền máu",
    i18n: "baoCao.bc30",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-30",
    accessRoles: [ROLES["BAO_CAO"].BC30],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC31. Báo cáo nhập xuất tồn kho máu",
    i18n: "baoCao.bc31",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-31",
    accessRoles: [ROLES["BAO_CAO"].BC31],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC32. Báo cáo doanh thu theo khoa chỉ định",
    i18n: "baoCao.bc32",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-32",
    accessRoles: [ROLES["BAO_CAO"].BC32],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC33. Báo cáo thời gian thực hiện dịch vụ kỹ thuật",
    i18n: "baoCao.bc33",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-33",
    accessRoles: [ROLES["BAO_CAO"].BC33],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC34. Báo cáo thống kê thời gian khám",
    i18n: "baoCao.bc34",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-34",
    accessRoles: [ROLES["BAO_CAO"].BC34],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC35. Báo cáo thời gian chờ khoa Khám bệnh",
    i18n: "baoCao.bc35",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-35",
    accessRoles: [ROLES["BAO_CAO"].BC35],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC36. Báo cáo doanh thu tiền giường yêu cầu",
    i18n: "baoCao.bc36",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-36",
    accessRoles: [ROLES["BAO_CAO"].BC36],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC37. Báo cáo thời gian chờ khoa Khám bệnh",
    i18n: "baoCao.bc37",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-37",
    accessRoles: [ROLES["BAO_CAO"].BC37],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC38. Báo cáo chi tiết kết quả xét nghiệm",
    i18n: "baoCao.bc38",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-38",
    accessRoles: [ROLES["BAO_CAO"].BC38],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC39. Báo cáo tổng hợp chi theo loại đối tượng",
    i18n: "baoCao.bc39",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-39",
    accessRoles: [ROLES["BAO_CAO"].BC39],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC40. Báo cáo Chi trả tiền công cho Người thực hiện",
    i18n: "baoCao.bc40",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-40",
    accessRoles: [ROLES["BAO_CAO"].BC40],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC41. Báo cáo NB nội trú có qua khoa đẻ và khoa phẫu thuật",
    i18n: "baoCao.bc41",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-41",
    accessRoles: [ROLES["BAO_CAO"].BC41],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "BC42. Báo cáo tổng hợp chỉ định dịch vụ",
    i18n: "baoCao.bc42",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bc-42",
    accessRoles: [ROLES["BAO_CAO"].BC42],
    group: NHOM_BAO_CAO.BAO_CAO_DICH_VU,
    capitalizeTitle: false,
  },
  {
    title: "PC01. Báo cáo tổng hợp danh sách bệnh nhân (Thuốc pha chế)",
    i18n: "baoCao.pc01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pc-01",
    accessRoles: [ROLES["BAO_CAO"].PC01],
    group: NHOM_BAO_CAO.BAO_CAO_PHA_CHE_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "PC02. Báo cáo tổng kết số liệu",
    i18n: "baoCao.pc02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pc-02",
    accessRoles: [ROLES["BAO_CAO"].PC02],
    group: NHOM_BAO_CAO.BAO_CAO_PHA_CHE_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "PC04. Báo cáo thống kê thuốc sử dụng",
    i18n: "baoCao.pc04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pc-04",
    accessRoles: [ROLES["BAO_CAO"].PC04],
    group: NHOM_BAO_CAO.BAO_CAO_PHA_CHE_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "TC01. Báo cáo chi tiết thu chi người bệnh ngoại trú",
    i18n: "baoCao.tc01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tong-hop-thu-tien-nb",
    accessRoles: [ROLES["BAO_CAO"].TONG_HOP_THU_TIEN_NB],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.tc01_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_1",
    accessRoles: [ROLES["BAO_CAO"].TC01_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.2. Báo cáo tổng hợp viện phí",
    i18n: "baoCao.tc01_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_2",
    accessRoles: [ROLES["BAO_CAO"].TC01_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.3. Báo cáo danh sách người bệnh hoàn tiền theo ngân hàng",
    i18n: "baoCao.tc01_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_3",
    accessRoles: [ROLES["BAO_CAO"].TC01_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.4. Báo cáo Tổng hợp biên lai thanh toán phòng khám",
    i18n: "baoCao.tc01_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_4",
    accessRoles: [ROLES["BAO_CAO"].TC01_4],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.5. Báo cáo doanh thu phòng khám",
    i18n: "baoCao.tc01_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_5",
    accessRoles: [ROLES["BAO_CAO"].TC01_5],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.6. Báo cáo Hoàn biên lai",
    i18n: "baoCao.tc01_6",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_6",
    accessRoles: [ROLES["BAO_CAO"].TC01_6],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.7. Báo cáo biên lai thanh toán phòng khám Bảo hiểm Y tế",
    i18n: "baoCao.tc01_7",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_7",
    accessRoles: [ROLES["BAO_CAO"].TC01_7],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.8. Báo cáo biên lai thanh toán ra viện",
    i18n: "baoCao.tc01_8",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_8",
    accessRoles: [ROLES["BAO_CAO"].TC01_8],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.9. Bảng tổng hợp chi tiền",
    i18n: "baoCao.tc01_9",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_9",
    accessRoles: [ROLES["BAO_CAO"].TC01_9],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.10. Báo cáo thu tiền người bệnh theo QRCODE nhà thuốc",
    i18n: "baoCao.tc01_10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_10",
    accessRoles: [ROLES["BAO_CAO"].TC01_10],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.11. Báo cáo tổng hợp ngoại trú",
    i18n: "baoCao.tc01_11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_11",
    accessRoles: [ROLES["BAO_CAO"].TC01_11],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.12. Báo cáo tổng hợp hoàn phòng khám",
    i18n: "baoCao.tc01_12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_12",
    accessRoles: [ROLES["BAO_CAO"].TC01_12],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.13. Báo cáo tổng hợp thanh toán viện phí nội trú",
    i18n: "baoCao.tc01_13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_13",
    accessRoles: [ROLES["BAO_CAO"].TC01_13],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.14. Báo cáo tổng hợp thu khám và xét nghiệm phòng khám",
    i18n: "baoCao.tc01_14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_14",
    accessRoles: [ROLES["BAO_CAO"].TC01_14],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.15 Báo cáo chi tiết doanh thu khám chữa bệnh",
    i18n: "baoCao.tc01_15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_15",
    accessRoles: [ROLES["BAO_CAO"].TC01_15],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC01.16. Báo cáo đối soát kết nối ngân hàng theo QRCODE",
    i18n: "baoCao.tc01_16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc01_16",
    accessRoles: [ROLES["BAO_CAO"].TC01_16],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC02. Bảng tổng hợp chi tiết thu chi theo thu ngân",
    i18n: "baoCao.tc02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tong-hop-chi-tiet-thu-chi-theo-thu-ngan",
    accessRoles: [ROLES["BAO_CAO"].TC02],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC02.1 Báo cáo tổng hợp Hóa đơn",
    i18n: "baoCao.tc02_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc02_1",
    accessRoles: [ROLES["BAO_CAO"].TC02_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC02.2 Báo cáo Chi tiết hoá đơn thanh toán",
    i18n: "baoCao.tc02_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc02_2",
    accessRoles: [ROLES["BAO_CAO"].TC02_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC03. Báo cáo sử dụng hóa đơn",
    i18n: "baoCao.tc03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc03",
    accessRoles: [ROLES["BAO_CAO"].TC03],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC03.2. Báo cáo tổng hợp hóa đơn",
    i18n: "baoCao.tc03_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc03_2",
    accessRoles: [ROLES["BAO_CAO"].TC03_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC04. Báo cáo chi tiết sử dụng hóa đơn",
    i18n: "baoCao.tc04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc-04",
    accessRoles: [ROLES["BAO_CAO"].TC04],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC05. Báo cáo thu tiền theo bác sĩ khám",
    i18n: "baoCao.tc05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc05",
    accessRoles: [ROLES["BAO_CAO"].TC05],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC06. Tổng hợp chi phí khám bệnh, chữa bệnh của người tham gia bảo hiểm y tế",
    i18n: "baoCao.tc06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc06",
    accessRoles: [ROLES["BAO_CAO"].TC06],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC06.1. Tổng hợp chi phí khám bệnh, chữa bệnh của NB không BHYT",
    i18n: "baoCao.tc06_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc06-1",
    accessRoles: [ROLES["BAO_CAO"].TC06_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC06.4. Báo cáo chi tiết thanh toán viện phí theo nhóm dịch vụ",
    i18n: "baoCao.tc06_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc06-4",
    accessRoles: [ROLES["BAO_CAO"].TC06_4],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC06.5. Báo cáo tổng hợp thanh toán viện phí theo ngày",
    i18n: "baoCao.tc06_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc06-5",
    accessRoles: [ROLES["BAO_CAO"].TC06_5],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC07. Báo cáo thu tiền dịch vụ",
    i18n: "baoCao.tc07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc07",
    accessRoles: [ROLES["BAO_CAO"].TC07],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC08. Báo cáo thu tiền dịch vụ theo yêu cầu",
    i18n: "baoCao.tc08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc08",
    accessRoles: [ROLES["BAO_CAO"].TC08],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC09. Báo cáo thu tiền theo loại tiền",
    i18n: "baoCao.tc09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc09",
    accessRoles: [ROLES["BAO_CAO"].TC09],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC10. Tổng hợp doanh thu TNDN",
    i18n: "baoCao.tc10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc10",
    accessRoles: [ROLES["BAO_CAO"].TC10],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC11. Báo cáo tổng hợp doanh số",
    i18n: "baoCao.tc11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc11",
    accessRoles: [ROLES["BAO_CAO"].TC11],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC12. Theo dõi tình hình tạm ứng và chi phí điều trị của Người bệnh",
    i18n: "baoCao.tc12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc12",
    accessRoles: [ROLES["BAO_CAO"].TC12],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC12.1 Tổng kết số tiền còn lại của bệnh nhân",
    i18n: "baoCao.tc12_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc12_1",
    accessRoles: [
      ROLES["BAO_CAO"].TC12_1_KHOA_THEO_TAI_KHOAN,
      ROLES["BAO_CAO"].TC12_1_TAT_CA_KHOA,
    ],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC13. Báo cáo danh sách người bệnh tạm ứng",
    i18n: "baoCao.tc13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc13",
    accessRoles: [ROLES["BAO_CAO"].TC13],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC13.1 Bảng kê thu tạm ứng vào viện",
    i18n: "baoCao.tc13_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc13_1",
    accessRoles: [ROLES["BAO_CAO"].TC13_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC13.2 Bảng kê chi hoàn ứng ra viện",
    i18n: "baoCao.tc13_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc13_2",
    accessRoles: [ROLES["BAO_CAO"].TC13_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC13.3 Báo cáo chi tiết thu ký quỹ",
    i18n: "baoCao.tc13_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc13_3",
    accessRoles: [ROLES["BAO_CAO"].TC13_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC14. Báo cáo tổng hợp tiền tạm ứng",
    i18n: "baoCao.tc14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc14",
    accessRoles: [ROLES["BAO_CAO"].TC14],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC14.1 Báo cáo tạm ứng",
    i18n: "baoCao.tc14_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc14_1",
    accessRoles: [ROLES["BAO_CAO"].TC14_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC15. Báo cáo thu và hoàn tạm ứng",
    i18n: "baoCao.tc15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15",
    accessRoles: [ROLES["BAO_CAO"].TC15],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC15.1 Báo cáo hoàn ký quỹ",
    i18n: "baoCao.tc15_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15_1",
    accessRoles: [ROLES["BAO_CAO"].TC15_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC15.2 Báo cáo thu chi viện phí, tạm ứng",
    i18n: "baoCao.tc15_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15_2",
    accessRoles: [ROLES["BAO_CAO"].TC15_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC15.3 Báo cáo tổng hợp thu viện phí, tạm ứng theo nhóm loại đối tượng",
    i18n: "baoCao.tc15_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15_3",
    accessRoles: [ROLES["BAO_CAO"].TC15_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC15.4 Báo cáo tổng hợp đề nghị chi tiền hoàn ứng",
    i18n: "baoCao.tc15_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15_4",
    accessRoles: [ROLES["BAO_CAO"].TC15_4],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC15.5 Báo cáo tổng hợp thu hoàn theo nhân viên",
    i18n: "baoCao.tc15_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc15_5",
    accessRoles: [ROLES["BAO_CAO"].TC15_5],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC16. Báo cáo tạm ứng - hoàn ứng của NB",
    i18n: "baoCao.tc16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc16",
    accessRoles: [ROLES["BAO_CAO"].TC16],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC16.1 Báo cáo số hoàn tạm giữ",
    i18n: "baoCao.tc16_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc16_1",
    accessRoles: [ROLES["BAO_CAO"].TC16_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC17. Báo cáo theo dõi tình hình tạm ứng theo nhà thu ngân",
    i18n: "baoCao.tc17",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc17",
    accessRoles: [ROLES["BAO_CAO"].TC17],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC17.1. Báo cáo theo dõi tình hình tạm ứng trong tháng",
    i18n: "baoCao.tc17_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc17_1",
    accessRoles: [ROLES["BAO_CAO"].TC17_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC17.2. Báo cáo chi tiết tạm ứng của người bệnh",
    i18n: "baoCao.tc17_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc17_2",
    accessRoles: [ROLES["BAO_CAO"].TC17_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC17.3. Báo cáo tồn tạm ứng theo ngày",
    i18n: "baoCao.tc17_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc17_3",
    accessRoles: [ROLES["BAO_CAO"].TC17_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC18. Báo cáo số dư tạm ứng lũy kế",
    i18n: "baoCao.tc18",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc18",
    accessRoles: [ROLES["BAO_CAO"].TC18],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC18.1. Báo cáo Số dư tạm ứng lũy kế",
    i18n: "baoCao.tc18_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc18_1",
    accessRoles: [ROLES["BAO_CAO"].TC18_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC18.2. Báo cáo số dư tạm ứng",
    i18n: "baoCao.tc18_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc18_2",
    accessRoles: [ROLES["BAO_CAO"].TC18_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC19. Mẫu 19,20,21",
    i18n: "baoCao.tc19",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc19",
    accessRoles: [ROLES["BAO_CAO"].TC19],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC20. Bảng chấm công các dịch vụ theo yêu cầu ngày thường",
    i18n: "baoCao.tc20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc20",
    accessRoles: [ROLES["BAO_CAO"].TC20],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC20.1 Bảng chia tiền dịch vụ khám theo yêu cầu",
    i18n: "baoCao.tc20_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc20.1",
    accessRoles: [ROLES["BAO_CAO"].TC20_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC21 Bảng tổng hợp thu dịch vụ KCB",
    i18n: "baoCao.tc21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc21",
    accessRoles: [ROLES["BAO_CAO"].TC21],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC21.1 Báo cáo chi tiền dịch vụ",
    i18n: "baoCao.tc21_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc21_1",
    accessRoles: [ROLES["BAO_CAO"].TC21_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC21.2 Báo cáo chi tiết chi tiền dịch vụ",
    i18n: "baoCao.tc21_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc21_2",
    accessRoles: [ROLES["BAO_CAO"].TC21_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC21.3 Báo cáo chênh lệch tiền dịch vụ NG và TG",
    i18n: "baoCao.tc21_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc21_3",
    accessRoles: [ROLES["BAO_CAO"].TC21_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC21.4 Báo cáo chi tiết chi tiền dịch vụ",
    i18n: "baoCao.tc21_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc21_4",
    accessRoles: [ROLES["BAO_CAO"].TC21_4],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC22 Bảng kê chi tiết thu dịch vụ KCB",
    i18n: "baoCao.tc22",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc22",
    accessRoles: [ROLES["BAO_CAO"].TC22],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC22.1. Báo cáo tồng hợp lệ phí dịch vụ ngoại trú",
    i18n: "baoCao.tc22_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc22.1",
    accessRoles: [ROLES["BAO_CAO"].TC22_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC22.2. Báo cáo tồng hợp lệ phí dịch vụ nội trú",
    i18n: "baoCao.tc22_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc22.2",
    accessRoles: [ROLES["BAO_CAO"].TC22_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC22.3. Báo cáo chi tiết tiền dịch vụ",
    i18n: "baoCao.tc22_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc22_3",
    accessRoles: [ROLES["BAO_CAO"].TC22_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC23 Bảng tổng hợp thu dịch vụ theo khoa thực hiện",
    i18n: "baoCao.tc23",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc23",
    accessRoles: [ROLES["BAO_CAO"].TC23],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC24 Phân tích cơ cấu thu chi theo khoa chỉ định",
    i18n: "baoCao.tc24",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc24",
    accessRoles: [ROLES["BAO_CAO"].TC24],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC25. Báo cáo tổng hợp tiền theo thời gian",
    i18n: "baoCao.tc25",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc25",
    accessRoles: [ROLES["BAO_CAO"].TC25],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC26. Báo cáo sử dụng voucher",
    i18n: "baoCao.tc26",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc26",
    accessRoles: [ROLES["BAO_CAO"].TC26],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC27. Báo cáo tổng hợp doanh thu theo loại đối tượng",
    i18n: "baoCao.tc27",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc27",
    accessRoles: [ROLES["BAO_CAO"].TC27],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC28. Báo cáo chi tiết chi phí người bệnh điều trị nội trú ra viện ",
    i18n: "baoCao.tc28",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc28",
    accessRoles: [ROLES["BAO_CAO"].TC28],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC28.1 Báo cáo thu chi viện phí, tạm ứng chi tiết theo NB",
    i18n: "baoCao.tc28_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc28_1",
    accessRoles: [ROLES["BAO_CAO"].TC28_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC29. Danh sách người bệnh chưa thanh toán",
    i18n: "baoCao.tc29",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc29",
    accessRoles: [ROLES["BAO_CAO"].TC29],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC29.1 Danh sách người bệnh đã thanh toán",
    i18n: "baoCao.tc29_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc29_1",
    accessRoles: [ROLES["BAO_CAO"].TC29_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC29.2 Danh sách hồ sơ chưa đẩy quyết toán",
    i18n: "baoCao.tc29_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc29_2",
    accessRoles: [ROLES["BAO_CAO"].TC29_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC31. Báo cáo chi tiết tài trợ theo nhà tài trợ",
    i18n: "baoCao.tc31",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc31",
    accessRoles: [ROLES["BAO_CAO"].TC31],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC33. Báo cáo danh sách nb ngoại trú/nội trú/hộ nghèo đề nghị thanh toán - mẫu 80",
    i18n: "baoCao.tc33",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc33",
    accessRoles: [ROLES["BAO_CAO"].TC33],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC34. Báo cáo tổng hợp thanh toán viện phí ",
    i18n: "baoCao.tc34",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc34",
    accessRoles: [ROLES["BAO_CAO"].TC34],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC36. Báo cáo ký quỹ đã tất toán",
    i18n: "baoCao.tc36",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc36",
    accessRoles: [ROLES["BAO_CAO"].TC36],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC37. Bảng tổng hợp thu KCB",
    i18n: "baoCao.tc37",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc37",
    accessRoles: [ROLES["BAO_CAO"].TC37],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC38. Báo cáo doanh thu chi tiết dịch vụ theo gói trong tháng",
    i18n: "baoCao.tc38",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc38",
    accessRoles: [ROLES["BAO_CAO"].TC38],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC39. Báo cáo thanh toán đơn thuốc nhà thuốc",
    i18n: "baoCao.tc39",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc39",
    accessRoles: [ROLES["BAO_CAO"].TC39],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC40. Báo cáo Tổng hợp thu - chi gói mổ 10 ngày",
    i18n: "baoCao.tc40",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc40",
    accessRoles: [ROLES["BAO_CAO"].TC40],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC41. Báo cáo chi phí không tính tiền",
    i18n: "baoCao.tc41",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc41",
    accessRoles: [ROLES["BAO_CAO"].TC41],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC41.1. Báo cáo danh sách dịch vụ nguồn khác",
    i18n: "baoCao.tc41_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc41_1",
    accessRoles: [ROLES["BAO_CAO"].TC41_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42. Báo cáo nộp tiền",
    i18n: "baoCao.tc42",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42",
    accessRoles: [ROLES["BAO_CAO"].TC42],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.1 Báo cáo nộp tiền chi tiết",
    i18n: "baoCao.tc42_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_1",
    accessRoles: [ROLES["BAO_CAO"].TC42_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.2 Báo cáo nộp tiền theo thu ngân",
    i18n: "baoCao.tc42_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_2",
    accessRoles: [ROLES["BAO_CAO"].TC42_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.3 Báo cáo hàng ngày theo phương thức thanh toán",
    i18n: "baoCao.tc42_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_3",
    accessRoles: [ROLES["BAO_CAO"].TC42_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.4 Báo cáo tổng hợp thu chi theo thu ngân",
    i18n: "baoCao.tc42_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_4",
    accessRoles: [ROLES["BAO_CAO"].TC42_4],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.5 Báo cáo tổng kết dịch vụ khám chữa bệnh",
    i18n: "baoCao.tc42_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_5",
    accessRoles: [ROLES["BAO_CAO"].TC42_5],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC42.6. Báo cáo tổng hợp nộp tiền theo thu ngân",
    i18n: "baoCao.tc42_6",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc42_6",
    accessRoles: [ROLES["BAO_CAO"].TC42_6],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC46. Danh sách người bệnh BHYT đề nghị thanh toán tổng hợp",
    i18n: "baoCao.tc46",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc46",
    accessRoles: [ROLES["BAO_CAO"].TC46],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC48. Báo cáo thống kê thuốc theo bác sĩ chỉ định",
    i18n: "baoCao.tc48",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc48",
    accessRoles: [ROLES["BAO_CAO"].TC48],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC49. Báo cáo chi tiết sử dụng voucher",
    i18n: "baoCao.tc49",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc49",
    accessRoles: [ROLES["BAO_CAO"].TC49],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC50. Báo cáo chấm công tiền dịch vụ theo từng bác sĩ",
    i18n: "baoCao.tc50",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc50",
    accessRoles: [ROLES["BAO_CAO"].TC50],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC51. Báo cáo tổng hợp doanh thu theo người bệnh",
    i18n: "baoCao.tc51",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc51",
    accessRoles: [ROLES["BAO_CAO"].TC51],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC55.1 Báo cáo tổng hợp chi tiết doanh thu theo bác sĩ chỉ định",
    i18n: "baoCao.tc51_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc51_1",
    accessRoles: [ROLES["BAO_CAO"].TC51_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC53. Báo cáo tổng chi phí của NB tại các khoa điều trị",
    i18n: "baoCao.tc53",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc53",
    accessRoles: [ROLES["BAO_CAO"].TC53],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC54. Báo cáo Tổng thu viện phí NB sử dụng dịch vụ của Trung tâm điều trị TYC CNC",
    i18n: "baoCao.tc54",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc54",
    accessRoles: [ROLES["BAO_CAO"].TC54],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC54.1 Báo cáo chi tiết dịch vụ của NB tại Trung tâm điều trị TYC CNC",
    i18n: "baoCao.tc54_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc54_1",
    accessRoles: [ROLES["BAO_CAO"].TC54_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.tc55",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc55",
    accessRoles: [ROLES["BAO_CAO"].TC55],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC55.1. Tổng thu viện phí chi tiết người bệnh sử dụng dịch vụ theo yêu cầu/ BHYT (CNC)",
    i18n: "baoCao.tc55_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc55_1",
    accessRoles: [ROLES["BAO_CAO"].TC55_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC56. Báo cáo doanh thu theo dịch vụ",
    i18n: "baoCao.tc56",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc56",
    accessRoles: [ROLES["BAO_CAO"].TC56],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC57. Báo cáo tổng hợp lệ phí dịch vụ ngoài điều trị",
    i18n: "baoCao.tc57",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc57",
    accessRoles: [ROLES["BAO_CAO"].TC57],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC57.1. Báo cáo chi tiết lệ phí dịch vụ ngoài điều trị",
    i18n: "baoCao.tc57_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc57_1",
    accessRoles: [ROLES["BAO_CAO"].TC57_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC58. Báo cáo tổng hợp lệ phí bệnh nhân miễn phí giám đốc duyệt",
    i18n: "baoCao.tc58",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc58",
    accessRoles: [ROLES["BAO_CAO"].TC58],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC58.1 Báo cáo tổng hợp chi phí khám bệnh, chữa bệnh của người bệnh miễn phí",
    i18n: "baoCao.tc58_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc58_1",
    accessRoles: [ROLES["BAO_CAO"].TC58_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC59. Báo cáo bảng tổng hợp thu viện phí",
    i18n: "baoCao.tc59",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc59",
    accessRoles: [ROLES["BAO_CAO"].TC59],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC59.1 Báo cáo tổng hợp thanh toán viện phí theo nhóm dịch vụ",
    i18n: "baoCao.tc59_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc59_1",
    accessRoles: [ROLES["BAO_CAO"].TC59_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC59.2 Báo cáo chi tiết dịch vụ thực hiện",
    i18n: "baoCao.tc59_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc59_2",
    accessRoles: [ROLES["BAO_CAO"].TC59_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC59.3 Danh sách chỉ định và viện phí",
    i18n: "baoCao.tc59_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc59_3",
    accessRoles: [ROLES["BAO_CAO"].TC59_3],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC60. Báo cáo doanh thu ngoại trú/nội trú",
    i18n: "baoCao.tc60",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc60",
    accessRoles: [ROLES["BAO_CAO"].TC60],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC61. Tổng hợp thu chi viện phí theo bác sĩ chỉ định",
    i18n: "baoCao.tc61",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc61",
    accessRoles: [ROLES["BAO_CAO"].TC61],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC62. Báo cáo tổng hợp thu tiền dịch vụ",
    i18n: "baoCao.tc62",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc62",
    accessRoles: [ROLES["BAO_CAO"].TC62],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC63 Báo cáo dịch vụ kỹ thuật theo giường theo yêu cầu",
    i18n: "baoCao.tc63",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc63",
    accessRoles: [ROLES["BAO_CAO"].TC63],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC63.1 Báo cáo chi tiết dịch vụ kỹ thuật theo giường theo yêu cầu",
    i18n: "baoCao.tc63_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc63_1",
    accessRoles: [ROLES["BAO_CAO"].TC63_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC64 Báo cáo giường theo yêu cầu",
    i18n: "baoCao.tc64",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc64",
    accessRoles: [ROLES["BAO_CAO"].TC64],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC64.1. Báo cáo chi tiết giường theo yêu cầu",
    i18n: "baoCao.tc64_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc64_1",
    accessRoles: [ROLES["BAO_CAO"].TC64_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC64.2. Báo cáo tổng hợp chi tiền giường tự chọn",
    i18n: "baoCao.tc64_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc64_2",
    accessRoles: [ROLES["BAO_CAO"].TC64_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC66. Báo cáo chi tiết doanh thu dịch vụ theo người bệnh",
    i18n: "baoCao.tc66",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc66",
    accessRoles: [ROLES["BAO_CAO"].TC66],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC67. Bảng kê nộp tiền viện phí",
    i18n: "baoCao.tc67",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc67",
    accessRoles: [ROLES["BAO_CAO"].TC67],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC67.1. Bảng kê thu tiền người bệnh",
    i18n: "baoCao.tc67_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc67_1",
    accessRoles: [ROLES["BAO_CAO"].TC67_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC67.2. Bảng tổng kết biên lai hoàn/hủy ngoại trú - nội trú",
    i18n: "baoCao.tc67_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc67_2",
    accessRoles: [ROLES["BAO_CAO"].TC67_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC68. Báo cáo Danh sách phiếu hủy",
    i18n: "baoCao.tc68",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc68",
    accessRoles: [ROLES["BAO_CAO"].TC68],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC66. Báo cáo danh sách NB miễn giảm",
    i18n: "baoCao.tc69",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc69",
    accessRoles: [ROLES["BAO_CAO"].TC69],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title:
      "TC69.1 Báo cáo Danh sách NB miễn giảm theo phương thức thanh toán Miễn giảm",
    i18n: "baoCao.tc69_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc69_1",
    accessRoles: [ROLES["BAO_CAO"].TC69_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC69.2. Danh sách bệnh nhân miễn giảm viện phí",
    i18n: "baoCao.tc69_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc69_2",
    accessRoles: [ROLES["BAO_CAO"].TC69_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC70. Báo cáo tổng hợp doanh thu theo khoa",
    i18n: "baoCao.tc70",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc70",
    accessRoles: [ROLES["BAO_CAO"].TC70],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC71. Báo cáo duyệt phơi BHYT theo nhân viên thu ngân",
    i18n: "baoCao.tc71",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc71",
    accessRoles: [ROLES["BAO_CAO"].TC71],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC72. Báo cáo phân nguồn dịch vụ khám chữa bệnh",
    i18n: "baoCao.tc72",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc72",
    accessRoles: [ROLES["BAO_CAO"].TC72],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC73. Báo cáo tổng hợp thu dịch vụ KCB Nội trú",
    i18n: "baoCao.tc73",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc73",
    accessRoles: [ROLES["BAO_CAO"].TC73],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC73.1. Báo cáo tổng hợp thu dịch vụ KCB Nội trú không thẻ",
    i18n: "baoCao.tc73_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc73_1",
    accessRoles: [ROLES["BAO_CAO"].TC73_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC74. Báo cáo tất toán chi phí",
    i18n: "baoCao.tc74",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc74",
    accessRoles: [ROLES["BAO_CAO"].TC74],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC75. Báo cáo tổng hợp BHYT thanh toán viện phí KCB",
    i18n: "baoCao.tc75",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc75",
    accessRoles: [ROLES["BAO_CAO"].TC75],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC75.1 Báo cáo chi tiết người bệnh BHYT thanh toán viện phí KCB",
    i18n: "baoCao.tc75_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc75_1",
    accessRoles: [ROLES["BAO_CAO"].TC75_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC76. Báo cáo tổng hợp tiền tang lễ và sao bệnh án bệnh nhân",
    i18n: "baoCao.tc76",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc76",
    accessRoles: [ROLES["BAO_CAO"].TC76],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC77. Báo cáo chi tiết thuốc, vật tư, hóa chất theo DVKT",
    i18n: "baoCao.tc77",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc77",
    accessRoles: [ROLES["BAO_CAO"].TC77],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC79. Bảng kê thanh toán ngoại trú",
    i18n: "baoCao.tc79",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc79",
    accessRoles: [ROLES["BAO_CAO"].TC79],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC79.1. Bảng kê thanh toán ngoại trú khám theo yêu cầu",
    i18n: "baoCao.tc79_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc79_1",
    accessRoles: [ROLES["BAO_CAO"].TC79_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC80. Bảng kê thanh toán nội trú",
    i18n: "baoCao.tc80",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc80",
    accessRoles: [ROLES["BAO_CAO"].TC80],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC81. Bảng kê thanh toán tế bào gốc",
    i18n: "baoCao.tc81",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc81",
    accessRoles: [ROLES["BAO_CAO"].TC81],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC81. Bảng kê chi tiết dịch vụ tế bào gốc",
    i18n: "baoCao.tc81_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc81_1",
    accessRoles: [ROLES["BAO_CAO"].TC81_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC80.1. Bảng kê thu tạm ứng",
    i18n: "baoCao.tc80_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc80_1",
    accessRoles: [ROLES["BAO_CAO"].TC80_1],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "TC80.2. Bảng kê biên lai hoàn trả",
    i18n: "baoCao.tc80_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tc80_2",
    accessRoles: [ROLES["BAO_CAO"].TC80_2],
    group: NHOM_BAO_CAO.BAO_CAO_TAI_CHINH,
    capitalizeTitle: false,
  },
  {
    title: "K01. Bảng kê hóa đơn nhập",
    i18n: "baoCao.k01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/bang-ke-hoa-don-nhap",
    accessRoles: [ROLES["BAO_CAO"].BANG_KE_HOA_DON_NHAP],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K01.1. Bảng kê chi tiết nhập theo nhà cung cấp",
    i18n: "baoCao.k01_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/nhap-theo-nha-cung-cap",
    accessRoles: [ROLES["BAO_CAO"].NHAP_THEO_NHA_CC],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02. Báo cáo xuất nhập tồn kho",
    i18n: "baoCao.k02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02",
    accessRoles: [ROLES["BAO_CAO"].XUAT_NHAP_TON_KHO],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.1. Báo cáo xuất nhập tồn kho lẻ",
    i18n: "baoCao.k02_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_1",
    accessRoles: [ROLES["BAO_CAO"].K02_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.2. Báo cáo xuất nhập tồn kho chi tiết",
    i18n: "baoCao.k02_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_2",
    accessRoles: [ROLES["BAO_CAO"].K02_2],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.3. Báo cáo xuất nhập tồn kho chi tiết nhiều kho",
    i18n: "baoCao.k02_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_3",
    accessRoles: [ROLES["BAO_CAO"].K02_3],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.4. Báo cáo xuất nhập tồn kho (NĐ)",
    i18n: "baoCao.k02_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_4",
    accessRoles: [ROLES["BAO_CAO"].K02_4],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.5. Báo cáo xuất nhập tồn kho lẻ toàn viện",
    i18n: "baoCao.k02_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_5",
    accessRoles: [ROLES["BAO_CAO"].K02_5],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.6. Báo cáo xuất nhập tồn toàn viện",
    i18n: "baoCao.k02_6",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_6",
    accessRoles: [ROLES["BAO_CAO"].K02_6],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.7. Báo cáo xuất nhập tồn toàn viện",
    i18n: "baoCao.k02_7",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_7",
    accessRoles: [ROLES["BAO_CAO"].XUAT_NHAP_TON_KHO],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.8. Báo cáo nhập xuất tồn kho toàn viện",
    i18n: "baoCao.k02_8",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_8",
    accessRoles: [ROLES["BAO_CAO"].K02_8],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K02.10. Báo cáo nhập xuất tồn kho toàn viện",
    i18n: "baoCao.k02_10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k02_10",
    accessRoles: [ROLES["BAO_CAO"].K02_10],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K03. Báo cáo chi tiết xuất kho",
    i18n: "baoCao.k03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k03",
    accessRoles: [ROLES["BAO_CAO"].CHI_TIET_XUAT_KHO],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K04 Báo cáo thẻ kho",
    i18n: "baoCao.k04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k04",
    accessRoles: [ROLES["BAO_CAO"].THE_KHO],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K04.1 Báo cáo thẻ kho năm",
    i18n: "baoCao.k04_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k04_1",
    accessRoles: [ROLES["BAO_CAO"].K04_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K04.2 Báo cáo thẻ kho tổng hợp",
    i18n: "baoCao.k04_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k04_2",
    accessRoles: [ROLES["BAO_CAO"].K04_2],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K04.3 Báo cáo kho thường quy",
    i18n: "baoCao.k04_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k04_3",
    accessRoles: [ROLES["BAO_CAO"].K04_3],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K04.4 Báo cáo tổng hợp tồn kho dược",
    i18n: "baoCao.k04_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k04_4",
    accessRoles: [ROLES["BAO_CAO"].K04_4],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K05. Báo cáo chi tiết nhập kho",
    i18n: "baoCao.k05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k05",
    accessRoles: [ROLES["BAO_CAO"].CHI_TIET_NHAP_KHO],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K05.1 Báo cáo tổng hợp phiếu nhập kho",
    i18n: "baoCao.k05_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k05_1",
    accessRoles: [ROLES["BAO_CAO"].K05_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K07. Biên bản kiểm kê tồn kho",
    i18n: "baoCao.k07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k07",
    accessRoles: [ROLES["BAO_CAO"].K07],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K07.1. Biên bản kiểm kê thực tế",
    i18n: "baoCao.k07_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k07_1",
    accessRoles: [ROLES["BAO_CAO"].K07_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K08. Biên bản kiểm nhập",
    i18n: "baoCao.k08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k08",
    accessRoles: [ROLES["BAO_CAO"].K08],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K10. Báo cáo tổng hợp xuất",
    i18n: "baoCao.k10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k10",
    accessRoles: [ROLES["BAO_CAO"].K10],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K11. Báo cáo tồn kho theo lô",
    i18n: "baoCao.k11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k11",
    accessRoles: [ROLES["BAO_CAO"].K11],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K12. Báo cáo chi tiết sử dụng hàng hóa thông thường",
    i18n: "baoCao.k12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k12",
    accessRoles: [ROLES["BAO_CAO"].K12],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K13. Báo cáo tổng hợp nhập",
    i18n: "baoCao.k13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k13",
    accessRoles: [ROLES["BAO_CAO"].K13],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K14. Báo cáo chi tiết xuất thuốc ngoại trú",
    i18n: "baoCao.k14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k14",
    accessRoles: [ROLES["BAO_CAO"].K14],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K14.1. Báo cáo chi tiết xuất thuốc ngoại trú",
    i18n: "baoCao.k14_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k14_1",
    accessRoles: [ROLES["BAO_CAO"].K14_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K14.2. Báo cáo chi tiết xuất thuốc ngoại trú",
    i18n: "baoCao.k14_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k14_2",
    accessRoles: [ROLES["BAO_CAO"].K14_2],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K15. Báo cáo dự trù hàng hóa theo tháng",
    i18n: "baoCao.k15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k15",
    accessRoles: [ROLES["BAO_CAO"].K15],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "KVT04. Báo cáo nhập xuất tồn vật tư định mức",
    i18n: "baoCao.kvt04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt04",
    accessRoles: [ROLES["BAO_CAO"].KVT04],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "K20. Thống kê thuốc sử dụng bệnh nhân",
    i18n: "baoCao.k20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k20",
    accessRoles: [ROLES["BAO_CAO"].K20],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K20.1. Báo cáo thực hiện y lệnh",
    i18n: "baoCao.k20_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k20_1",
    accessRoles: [ROLES["BAO_CAO"].K20_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title:
      "K20.2. Thống kê thuốc sử dụng bệnh nhân (không chia chi tiết theo ngày)",
    i18n: "baoCao.k20_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k20_2",
    accessRoles: [ROLES["BAO_CAO"].K20_2],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K20.3. Thống kê thuốc sử dụng bệnh nhân (Lĩnh,trả)",
    i18n: "baoCao.k20_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k20_3",
    accessRoles: [ROLES["BAO_CAO"].K20_3],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K21. Giấy đề nghị thanh toán",
    i18n: "baoCao.k21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k21",
    accessRoles: [ROLES["BAO_CAO"].K21],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k22",
    title: "K22. Báo cáo thuốc sử dụng theo tháng",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k22",
    accessRoles: [ROLES["BAO_CAO"].K22],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K23. Báo cáo sử dụng thuốc theo khoa",
    i18n: "baoCao.k23",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k23",
    accessRoles: [ROLES["BAO_CAO"].K23],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k24",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k24",
    accessRoles: [ROLES["BAO_CAO"].K24],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k25",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k25",
    accessRoles: [ROLES["BAO_CAO"].K25],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k26",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k26",
    accessRoles: [ROLES["BAO_CAO"].K26],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k27",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k27",
    accessRoles: [ROLES["BAO_CAO"].K27],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k28",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k28",
    accessRoles: [ROLES["BAO_CAO"].K28],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k29",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k29",
    accessRoles: [ROLES["BAO_CAO"].K29],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k30",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k30",
    accessRoles: [ROLES["BAO_CAO"].K30],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k32",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k32",
    accessRoles: [ROLES["BAO_CAO"].K32],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k31",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k31",
    accessRoles: [ROLES["BAO_CAO"].K31],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k33",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k33",
    accessRoles: [ROLES["BAO_CAO"].K33],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k34",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k34",
    accessRoles: [ROLES["BAO_CAO"].K34],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k35",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k35",
    accessRoles: [ROLES["BAO_CAO"].K35],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k36",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k36",
    accessRoles: [ROLES["BAO_CAO"].K36],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k37",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k37",
    accessRoles: [ROLES["BAO_CAO"].K37],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    i18n: "baoCao.k38",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k38",
    accessRoles: [ROLES["BAO_CAO"].K38],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K39. Báo cáo Hàng tiêu hao thuốc/VT theo Khoa",
    i18n: "baoCao.k39",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k39",
    accessRoles: [ROLES["BAO_CAO"].K39],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K40. Báo cáo tổng hợp xuất kho từ kho - đến kho",
    i18n: "baoCao.k40",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k40",
    accessRoles: [ROLES["BAO_CAO"].K40],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K42. Báo cáo danh sách thuốc xuất chi tiết",
    i18n: "baoCao.k42",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k42",
    accessRoles: [ROLES["BAO_CAO"].K42],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K43. Báo cáo chi tiết thuốc bệnh nhân sử dụng",
    i18n: "baoCao.k43",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k43",
    accessRoles: [ROLES["BAO_CAO"].K43],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K44. Phiếu truyền dịch và công khai thuốc",
    i18n: "baoCao.k44",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k44",
    accessRoles: [ROLES["BAO_CAO"].K44],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K45. Báo cáo danh sách thuốc xuất trả - chi tiết",
    i18n: "baoCao.k45",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k45",
    accessRoles: [ROLES["BAO_CAO"].K45],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K46. Báo cáo danh sách thuốc trả",
    i18n: "baoCao.k46",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k46",
    accessRoles: [ROLES["BAO_CAO"].K46],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K47. Báo cáo chi tiết thuốc sử dụng theo khoa",
    i18n: "baoCao.k47",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k47",
    accessRoles: [ROLES["BAO_CAO"].K47],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K48. Báo cáo tổng xuất theo kho",
    i18n: "baoCao.k48",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k48",
    accessRoles: [ROLES["BAO_CAO"].K48],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K49. Báo cáo tổng hợp phát thuốc ngoại trú",
    i18n: "baoCao.k49",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k49",
    accessRoles: [ROLES["BAO_CAO"].K49],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title:
      "K50.Báo cáo tiền thuốc xuất cho tủ trực các khoa (theo ngày kích phát)",
    i18n: "baoCao.k50",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k50",
    accessRoles: [ROLES["BAO_CAO"].K50],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K51. Báo cáo chi tiết phát thuốc ngoại trú",
    i18n: "baoCao.k51",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k51",
    accessRoles: [ROLES["BAO_CAO"].K51],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K52. Báo cáo phân tích DDD, DOT, LOT",
    i18n: "baoCao.k52",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k52",
    accessRoles: [ROLES["BAO_CAO"].K52],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K53. Báo cáo chi tiết tiền thuốc cho bệnh nhân toàn viện",
    i18n: "baoCao.k53",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k53",
    accessRoles: [ROLES["BAO_CAO"].K53],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K54. Báo cáo tư vấn thuốc cho người bệnh",
    i18n: "baoCao.k54",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k54",
    accessRoles: [ROLES["BAO_CAO"].K54],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K55. Báo cáo giám sát sử dụng thuốc",
    i18n: "baoCao.k55",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k55",
    accessRoles: [ROLES["BAO_CAO"].K55],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K56. Báo cáo tiền thuốc các khoa lâm sàng",
    i18n: "baoCao.k56",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k56",
    accessRoles: [ROLES["BAO_CAO"].K56],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K57. Báo cáo tiền thuốc các khoa lâm sàng",
    i18n: "baoCao.k57",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k57",
    accessRoles: [ROLES["BAO_CAO"].K57],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K58. Báo cáo tổng hợp xuất kho",
    i18n: "baoCao.k58",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k58",
    accessRoles: [ROLES["BAO_CAO"].K58],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K58.1. Báo cáo tổng hợp xuất kho",
    i18n: "baoCao.k58_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k58_1",
    accessRoles: [ROLES["BAO_CAO"].K58_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K59. Báo cáo kháng sinh cho người bệnh nội, ngoại trú",
    i18n: "baoCao.k59",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k59",
    accessRoles: [ROLES["BAO_CAO"].K59],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K60. Báo cáo Cấp phát đơn thuốc ngoại trú",
    i18n: "baoCao.k60",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k60",
    accessRoles: [ROLES["BAO_CAO"].K60],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K61. Báo cáo Tổng hợp tiền thuốc sử dụng",
    i18n: "baoCao.k61",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k61",
    accessRoles: [ROLES["BAO_CAO"].K61],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K62. Báo cáo tổng hợp phiếu xuất trả NCC",
    i18n: "baoCao.k62",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k62",
    accessRoles: [ROLES["BAO_CAO"].K62],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K63. Báo cáo Kiểm thuốc hàng ngày",
    i18n: "baoCao.k63",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k63",
    accessRoles: [ROLES["BAO_CAO"].K63],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K64. Báo cáo hủy thuốc",
    i18n: "baoCao.k64",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k64",
    accessRoles: [ROLES["BAO_CAO"].K64],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K65. Báo cáo tổng hợp tồn kho theo lô,date cần khóa",
    i18n: "baoCao.k65",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k65",
    accessRoles: [ROLES["BAO_CAO"].K65],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K66. Báo cáo danh sách hàng hóa chưa lập phiếu hoàn trả",
    i18n: "baoCao.k66",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k66",
    accessRoles: [ROLES["BAO_CAO"].K66],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K67. Báo cáo danh sách hàng hóa lẻ chưa lên phiếu lĩnh, trả",
    i18n: "baoCao.k67",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k67",
    accessRoles: [ROLES["BAO_CAO"].K67],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K68. Báo cáo xuất kho chi tiết theo ngày",
    i18n: "baoCao.k68",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k68",
    accessRoles: [ROLES["BAO_CAO"].K68],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K69. Báo cáo nhập trả từ khoa chi tiết",
    i18n: "baoCao.k69",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k69",
    accessRoles: [ROLES["BAO_CAO"].K69],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K70. Báo cáo thống kê người bệnh không lấy đơn thuốc",
    i18n: "baoCao.k70",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k70",
    accessRoles: [ROLES["BAO_CAO"].K70],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K71. Biên bản hủy thuốc",
    i18n: "baoCao.k71",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k71",
    accessRoles: [ROLES["BAO_CAO"].K71],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K72. Chi tiết thuốc xuất cho từng khoa",
    i18n: "baoCao.k72",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k72",
    accessRoles: [ROLES["BAO_CAO"].K72],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K73. Biên bản kiểm kê thực tế",
    i18n: "baoCao.k73",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k73",
    accessRoles: [ROLES["BAO_CAO"].K73],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K74. Danh sách xuất thuốc ra viện",
    i18n: "baoCao.k74",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k74",
    accessRoles: [ROLES["BAO_CAO"].K74],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K75. Danh sách xuất thuốc ra viện",
    i18n: "baoCao.k75",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k75",
    accessRoles: [ROLES["BAO_CAO"].K75],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K76. Báo cáo Danh sách toa thuốc ngoại trú từ 6 loại thuốc trở lên",
    i18n: "baoCao.k76",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k76",
    accessRoles: [ROLES["BAO_CAO"].K76],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K77. Báo cáo danh sách trả thuốc tái khám sớm",
    i18n: "baoCao.k77",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k77",
    accessRoles: [ROLES["BAO_CAO"].K77],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K78. Báo cáo sử dụng hóa chất",
    i18n: "baoCao.k78",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k78",
    accessRoles: [ROLES["BAO_CAO"].K79],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title:
      "K79. Báo cáo tình hình sử dụng các thuốc có hoạt chất, nồng độ, hàm lượng cùng các thuốc thuộc danh mục thuốc đấu thầu tập trung cấp địa phương",
    i18n: "baoCao.k79",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k79",
    accessRoles: [ROLES["BAO_CAO"].K79],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K80. Sổ theo dõi nhà cung cấp",
    i18n: "baoCao.k80",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k80",
    accessRoles: [ROLES["BAO_CAO"].K80],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K82. Bảng kê phiếu xuất kho",
    i18n: "baoCao.k82",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k82",
    accessRoles: [ROLES["BAO_CAO"].K82],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K84. Báo cáo dự trù thuốc",
    i18n: "baoCao.k84",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k84",
    accessRoles: [ROLES["BAO_CAO"].K84],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K01.2. Báo cáo nhập theo hàng hóa",
    i18n: "baoCao.k01_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k01_2",
    accessRoles: [ROLES["BAO_CAO"].K01_2],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "K01.3. Báo cáo nhập kho theo thầu",
    i18n: "baoCao.k01_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/k01_3",
    accessRoles: [ROLES["BAO_CAO"].K01_3],
    group: NHOM_BAO_CAO.BAO_CAO_KHO,
    capitalizeTitle: false,
  },
  {
    title: "KVT01.1. Báo cáo nhập kho theo nhà cung cấp",
    i18n: "baoCao.kvt01_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt01_1",
    accessRoles: [ROLES["BAO_CAO"].KVT01_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KVT02. Báo cáo chi tiết sử dụng hàng hóa KTC",
    i18n: "baoCao.kvt02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt02",
    accessRoles: [ROLES["BAO_CAO"].KVT02],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KVT03. Báo cáo sử dụng hàng hóa KTC",
    i18n: "baoCao.kvt03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt03",
    accessRoles: [ROLES["BAO_CAO"].KVT03],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KVT05. Báo cáo xuất hủy y dụng cụ",
    i18n: "baoCao.kvt05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt05",
    accessRoles: [ROLES["BAO_CAO"].KVT05],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KVT06. Báo cáo phát/trả vật tư",
    i18n: "baoCao.kvt06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt06",
    accessRoles: [ROLES["BAO_CAO"].KVT06],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KVT07. Báo cáo vật tư y tế quản lý tại khoa toàn bệnh viện",
    i18n: "baoCao.kvt07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kvt07",
    accessRoles: [ROLES["BAO_CAO"].KVT07],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_VT_HC,
    capitalizeTitle: false,
  },
  {
    title: "KNT01. Báo cáo thống kê thuốc bán theo bác sĩ",
    i18n: "baoCao.knt01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt01",
    accessRoles: [ROLES["BAO_CAO"].KNT01],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT02. Báo cáo tổng hợp thu tiền quầy thuốc",
    i18n: "baoCao.knt02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt02",
    accessRoles: [ROLES["BAO_CAO"].KNT02],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT03. Bảng chi tiết thu quầy thuốc theo ngày",
    i18n: "baoCao.knt03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt03",
    accessRoles: [ROLES["BAO_CAO"].KNT03],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT03.1. Bảng chi tiết thu quầy thuốc theo ngày (theo quyền xuất)",
    i18n: "baoCao.knt03_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt03_1",
    accessRoles: [ROLES["BAO_CAO"].KNT03_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT04. Báo cáo chi tiết thu tiền quầy thuốc",
    i18n: "baoCao.knt04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt04",
    accessRoles: [ROLES["BAO_CAO"].KNT04],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT05. Bảng kê bán hàng tổng hợp",
    i18n: "baoCao.knt05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt05",
    accessRoles: [ROLES["BAO_CAO"].KNT05],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT06. Sổ theo dõi khách hàng mua thuốc kiểm soát đặc biệt",
    i18n: "baoCao.knt06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt06",
    accessRoles: [ROLES["BAO_CAO"].KNT06],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT07. Sổ theo dõi xuất nhập tồn thuốc kiểm soát đặc biệt",
    i18n: "baoCao.knt07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt07",
    accessRoles: [ROLES["BAO_CAO"].KNT07],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT08. Báo cáo hoàn trả thuốc nhà thuốc",
    i18n: "baoCao.knt08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt08",
    accessRoles: [ROLES["BAO_CAO"].KNT08],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT08.1 Báo cáo hoàn trả thuốc theo VAT",
    i18n: "baoCao.knt08_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt08_1",
    accessRoles: [ROLES["BAO_CAO"].KNT08],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title:
      "KNT10. Báo cáo xuất dữ liệu phiếu nhập kho đẩy liên thông nhà thuốc",
    i18n: "baoCao.knt10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt10",
    accessRoles: [ROLES["BAO_CAO"].KNT10],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT11. Báo cáo phản ứng có hại của thuốc",
    i18n: "baoCao.knt11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt11",
    accessRoles: [ROLES["BAO_CAO"].KNT11],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT12. Bảng kê bán lẻ theo VAT",
    i18n: "baoCao.knt12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt12",
    accessRoles: [ROLES["BAO_CAO"].KNT12],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT13. Bảng kê bán lẻ tổng hợp theo VAT",
    i18n: "baoCao.knt13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt13",
    accessRoles: [ROLES["BAO_CAO"].KNT13],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT14. Biên bản kiểm kê",
    i18n: "baoCao.knt14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt14",
    accessRoles: [ROLES["BAO_CAO"].KNT14],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT15. Báo cáo doanh thu nhà thuốc",
    i18n: "baoCao.knt15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt15",
    accessRoles: [ROLES["BAO_CAO"].KNT15],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT15.1 Báo cáo chi tiết doanh thu và giá vốn nhà thuốc",
    i18n: "baoCao.knt15_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt15_1",
    accessRoles: [ROLES["BAO_CAO"].KNT15_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT16. Báo cáo chi tiết thu - chi nhà thuốc",
    i18n: "baoCao.knt16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt16",
    accessRoles: [ROLES["BAO_CAO"].KNT16],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT17. Danh sách khách hàng mua thuốc",
    i18n: "baoCao.knt17",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt17",
    accessRoles: [ROLES["BAO_CAO"].KNT17],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT18. Báo cáo thuốc chậm sử dụng",
    i18n: "baoCao.knt18",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt18",
    accessRoles: [ROLES["BAO_CAO"].KNT18],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT19. Doanh số bán thuốc bình ổn",
    i18n: "baoCao.knt19",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt19",
    accessRoles: [ROLES["BAO_CAO"].KNT19],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT20. Báo cáo xuất nhập tồn nhà thuốc",
    i18n: "baoCao.knt20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt20",
    accessRoles: [ROLES["BAO_CAO"].KNT20],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT21. Báo cáo doanh thu nhà thuốc (theo bác sĩ hoặc theo dịch vụ)",
    i18n: "baoCao.knt21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt21",
    accessRoles: [ROLES["BAO_CAO"].KNT21],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT22. Báo cáo tổng hợp thu tiền nhà thuốc của người bệnh",
    i18n: "baoCao.knt22",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt22",
    accessRoles: [ROLES["BAO_CAO"].KNT22],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT23. Báo cáo chi tiết dịch vụ nhà thuốc",
    i18n: "baoCao.knt23",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt23",
    accessRoles: [ROLES["BAO_CAO"].KNT23],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT24. Sổ theo dõi bán thuốc theo đơn",
    i18n: "baoCao.knt24",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt24",
    accessRoles: [ROLES["BAO_CAO"].KNT24],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT25. Báo cáo nhập xuất tồn kho VAT theo ngày",
    i18n: "baoCao.knt25",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt25",
    accessRoles: [ROLES["BAO_CAO"].KNT25],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KNT26. Bảng kê đơn thuốc bán lẻ",
    i18n: "baoCao.knt26",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/knt26",
    accessRoles: [ROLES["BAO_CAO"].KNT26],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_NHA_THUOC,
    capitalizeTitle: false,
  },
  {
    title: "KSK01. Báo cáo kết quả khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-01",
    accessRoles: [ROLES["BAO_CAO"].KSK01],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK01.1 Báo cáo kết quả khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk01_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-01_1",
    accessRoles: [ROLES["BAO_CAO"].KSK01_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title:
      "KSK02. Báo cáo thanh toán chi phí khám sức khỏe hợp đồng theo công ty",
    i18n: "baoCao.ksk02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-02",
    accessRoles: [ROLES["BAO_CAO"].KSK02],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK04 - Danh sách người bệnh lấy máu khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-04",
    accessRoles: [ROLES["BAO_CAO"].KSK04],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK05 - Danh sách người bệnh khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-05",
    accessRoles: [ROLES["BAO_CAO"].KSK05],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK12 - Danh sách công ty KSK theo trạng thái hợp đồng",
    i18n: "baoCao.ksk12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-12",
    accessRoles: [ROLES["BAO_CAO"].KSK12],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK13 - Báo cáo thanh toán hợp đồng KSK",
    i18n: "baoCao.ksk13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-13",
    accessRoles: [ROLES["BAO_CAO"].KSK13],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK15. Báo cáo kết quả khám",
    i18n: "baoCao.ksk15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-15",
    accessRoles: [ROLES["BAO_CAO"].KSK15],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK16. Báo cáo thống kê số lượng khám theo bác sĩ",
    i18n: "baoCao.ksk16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-16",
    accessRoles: [ROLES["BAO_CAO"].KSK16],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title:
      "KSK17. Báo cáo đề nghị thanh toán người bệnh khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk17",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-17",
    accessRoles: [ROLES["BAO_CAO"].KSK17],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title:
      "KSK18. Báo cáo chi tiết phiếu thu thanh toán người bệnh khám sức khỏe hợp đồng",
    i18n: "baoCao.ksk18",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-18",
    accessRoles: [ROLES["BAO_CAO"].KSK18],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK19. Báo cáo nghiệm thu đoàn",
    i18n: "baoCao.ksk19",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-19",
    accessRoles: [ROLES["BAO_CAO"].KSK19],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK20. Bảng tổng hợp, phân loại sức khỏe khám định kỳ",
    i18n: "baoCao.ksk20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-20",
    accessRoles: [ROLES["BAO_CAO"].KSK20],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK20.1. Bảng tổng hợp chi phí khám sức khỏe định kỳ",
    i18n: "baoCao.ksk20_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-20_1",
    accessRoles: [ROLES["BAO_CAO"].KSK20_1],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "KSK21. Báo cáo xuất chi tiết dịch vụ trong hợp đồng",
    i18n: "baoCao.ksk21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksk-21",
    accessRoles: [ROLES["BAO_CAO"].KSK21],
    group: NHOM_BAO_CAO.BAO_CAO_KHAM_SUC_KHOE,
    capitalizeTitle: false,
  },
  {
    title: "G01 - Báo cáo chi tiết thực hiện dịch vụ gói theo người bệnh",
    i18n: "baoCao.g01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-01",
    accessRoles: [ROLES["BAO_CAO"].G01],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G02 - Báo cáo chi tiết thực hiện dịch vụ",
    i18n: "baoCao.g02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-02",
    accessRoles: [ROLES["BAO_CAO"].G02],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G03 - Báo cáo chi tiết dịch vụ theo phiếu thu thanh toán",
    i18n: "baoCao.g03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-03",
    accessRoles: [ROLES["BAO_CAO"].G03],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G04 - Báo cáo Tổng hợp doanh thu",
    i18n: "baoCao.g04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-04",
    accessRoles: [ROLES["BAO_CAO"].G04],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G05 - Báo cáo công nợ phải thu khách hàng sử dụng gói",
    i18n: "baoCao.g05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-05",
    accessRoles: [ROLES["BAO_CAO"].G05],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G06 - Báo cáo phòng khám",
    i18n: "baoCao.g06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-06",
    accessRoles: [ROLES["BAO_CAO"].G06],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "G07 - Báo cáo chi tiết thực hiện liệu trình theo người bệnh",
    i18n: "baoCao.g07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/g-07",
    accessRoles: [ROLES["BAO_CAO"].G07],
    group: NHOM_BAO_CAO.BAO_CAO_GOI_LIEU_TRINH,
    capitalizeTitle: false,
  },
  {
    title: "KHTH01. Báo cáo hoạt động chuyên môn của trung tâm",
    i18n: "baoCao.khth01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-01",
    accessRoles: [ROLES["BAO_CAO"].KHTH01],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH02. Thống kê người bệnh theo mặt bệnh",
    i18n: "baoCao.khth02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-02",
    accessRoles: [ROLES["BAO_CAO"].KHTH02],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH03. Thống kê người bệnh ra viện",
    i18n: "baoCao.khth03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-03",
    accessRoles: [ROLES["BAO_CAO"].KHTH03],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH04. Báo cáo công suất sử dụng giường",
    i18n: "baoCao.khth04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-04",
    accessRoles: [ROLES["BAO_CAO"].KHTH04],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH05. Thống kê người bệnh chuyển tuyến",
    i18n: "baoCao.khth05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-05",
    accessRoles: [ROLES["BAO_CAO"].KHTH05],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH06. Thống kê người bệnh tử vong",
    i18n: "baoCao.khth06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-06",
    accessRoles: [ROLES["BAO_CAO"].KHTH06],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH07. Phản hồi thông tin người bệnh chuyển tuyến",
    i18n: "baoCao.khth07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-07",
    accessRoles: [ROLES["BAO_CAO"].KHTH07],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH08. Danh sách chi tiết NB chuyển khoa ra viện",
    i18n: "baoCao.khth08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-08",
    accessRoles: [ROLES["BAO_CAO"].KHTH08],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH08.1 Danh sách NB ra viện",
    i18n: "baoCao.khth08_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-08_1",
    accessRoles: [ROLES["BAO_CAO"].KHTH08_1],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH08.2 Danh sách chi tiết người bệnh ra viện",
    i18n: "baoCao.khth08_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-08_2",
    accessRoles: [ROLES["BAO_CAO"].KHTH08_2],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH09. Danh sách chi tiết NB chuyển tuyến",
    i18n: "baoCao.khth09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-09",
    accessRoles: [ROLES["BAO_CAO"].KHTH09],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH10. Danh sách chi tiết NB tử vong",
    i18n: "baoCao.khth10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-10",
    accessRoles: [ROLES["BAO_CAO"].KHTH10],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH11. Danh sách chi tiết NB đang điều trị",
    i18n: "baoCao.khth11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-11",
    accessRoles: [ROLES["BAO_CAO"].KHTH11],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH12. Danh sách chi tiết NB vào viện",
    i18n: "baoCao.khth12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-12",
    accessRoles: [ROLES["BAO_CAO"].KHTH12],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH12.1 Danh sách chi tiết NB vào viện",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-12_1",
    accessRoles: [ROLES["BAO_CAO"].KHTH12_1],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH13. Danh sách bệnh án lưu trữ",
    i18n: "baoCao.khth13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-13",
    accessRoles: [ROLES["BAO_CAO"].KHTH13],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH14. Báo cáo nhiễm theo TT54",
    i18n: "baoCao.khth14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-14",
    accessRoles: [ROLES["BAO_CAO"].KHTH14],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH15. Danh sách chi tiết NB vào khoa",
    i18n: "baoCao.khth15",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-15",
    accessRoles: [ROLES["BAO_CAO"].KHTH15],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH16. Danh sách sách chi tiết NB xuất khoa",
    i18n: "baoCao.khth16",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-16",
    accessRoles: [ROLES["BAO_CAO"].KHTH16],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH17. Cơ sở, giường bệnh và hoạt động khám chữa bệnh",
    i18n: "baoCao.khth17",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-17",
    accessRoles: [ROLES["BAO_CAO"].KHTH17],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH18. Danh sách giấy chứng nhận nghỉ việc hưởng BHXH",
    i18n: "baoCao.khth18",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-18",
    accessRoles: [ROLES["BAO_CAO"].KHTH18],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH19. Tình hình bệnh tật và tử vong tại bệnh viện theo ICD10",
    i18n: "baoCao.khth19",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-19",
    accessRoles: [ROLES["BAO_CAO"].KHTH19],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH20. Danh sách người bệnh theo hạn thẻ BHYT",
    i18n: "baoCao.khth20",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-20",
    accessRoles: [ROLES["BAO_CAO"].KHTH20],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH21. Báo cáo hoạt động giao ban",
    i18n: "baoCao.khth21",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-21",
    accessRoles: [ROLES["BAO_CAO"].KHTH21],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH22. Thống kê hoạt động điều trị bệnh án nội trú",
    i18n: "baoCao.khth22",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-22",
    accessRoles: [ROLES["BAO_CAO"].KHTH22],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH23. Báo cáo số liệu người già",
    i18n: "baoCao.khth23",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-23",
    accessRoles: [ROLES["BAO_CAO"].KHTH23],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH24. Báo cáo số liệu trẻ em",
    i18n: "baoCao.khth24",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-24",
    accessRoles: [ROLES["BAO_CAO"].KHTH24],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH25. Thống kê hoạt động phẫu thuật, thủ thuật",
    i18n: "baoCao.khth25",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-25",
    accessRoles: [ROLES["BAO_CAO"].KHTH25],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH26. Báo cáo thống kê hoạt động CLS",
    i18n: "baoCao.khth26",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-26",
    accessRoles: [ROLES["BAO_CAO"].KHTH26],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH27. Báo cáo Hoạt động khám chữa bệnh",
    i18n: "baoCao.khth27",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-27",
    accessRoles: [ROLES["BAO_CAO"].KHTH27],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH29. Báo cáo Thống kê Tình hình hoạt động khám bệnh",
    i18n: "baoCao.khth29",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-29",
    accessRoles: [ROLES["BAO_CAO"].KHTH29],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH30. Báo cáo Tai nạn thương tích",
    i18n: "baoCao.khth30",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-30",
    accessRoles: [ROLES["BAO_CAO"].KHTH30],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH31. Danh sách sản phụ đẻ",
    i18n: "baoCao.khth31",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-31",
    accessRoles: [ROLES["BAO_CAO"].KHTH31],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH33. Báo cáo tổng hợp thị lực sau phẫu thuật",
    i18n: "baoCao.khth33",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-33",
    accessRoles: [ROLES["BAO_CAO"].KHTH33],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title:
      "KHTH34. Báo cáo hoạt động phòng chống Sốt rét, HIV/AIDS, TNTT, Lao, Phong và Sức khỏe Tâm thần",
    i18n: "baoCao.khth34",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-34",
    accessRoles: [ROLES["BAO_CAO"].KHTH34],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH35. Phát hiện, quản lý điều trị bệnh không lây nhiễm",
    i18n: "baoCao.khth35",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-35",
    accessRoles: [ROLES["BAO_CAO"].KHTH35],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH37. Báo cáo thống kê số lượng Cấp cứu",
    i18n: "baoCao.khth37",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-37",
    accessRoles: [ROLES["BAO_CAO"].KHTH37],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH38. Báo cáo số liệu khám tại phòng khám",
    i18n: "baoCao.khth38",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-38",
    accessRoles: [ROLES["BAO_CAO"].KHTH38],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH40. Báo cáo Tổng số bệnh nhân thực hiện CLS theo khoa",
    i18n: "baoCao.khth40",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-40",
    accessRoles: [ROLES["BAO_CAO"].KHTH40],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH41. Báo cáo Tình hình thực hiện CLS tại các Phòng khám",
    i18n: "baoCao.khth41",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-41",
    accessRoles: [ROLES["BAO_CAO"].KHTH41],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH42. Báo cáo số liệu phẫu thuật",
    i18n: "baoCao.khth42",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-42",
    accessRoles: [ROLES["BAO_CAO"].KHTH42],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH43. Danh sách bệnh nhân khám bệnh",
    i18n: "baoCao.khth43",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-43",
    accessRoles: [ROLES["BAO_CAO"].KHTH43],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH44. Danh sách bệnh nhân vào cấp cứu",
    i18n: "baoCao.khth44",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-44",
    accessRoles: [ROLES["BAO_CAO"].KHTH44],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH45. Danh sách bệnh nhân nội trú",
    i18n: "baoCao.khth45",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-45",
    accessRoles: [ROLES["BAO_CAO"].KHTH45],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH46. Danh sách bệnh nhân phẫu thuật",
    i18n: "baoCao.khth46",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-46",
    accessRoles: [ROLES["BAO_CAO"].KHTH46],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH47. Sổ bàn giao người bệnh về phòng hậu phẫu",
    i18n: "baoCao.khth47",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-47",
    accessRoles: [ROLES["BAO_CAO"].KHTH47],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH48. Danh sách người bệnh VNeID",
    i18n: "baoCao.khth48",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-48",
    accessRoles: [ROLES["BAO_CAO"].KHTH48],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "KHTH49. Danh sách NB điều trị",
    i18n: "baoCao.khth49",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/khth-49",
    accessRoles: [ROLES["BAO_CAO"].KHTH49],
    group: NHOM_BAO_CAO.BAO_CAO_KE_HOACH_TONG_HOP,
    capitalizeTitle: false,
  },
  {
    title: "PTTT01. Danh sách phẫu thuật, thủ thuật",
    i18n: "baoCao.pttt01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-01",
    accessRoles: [ROLES["BAO_CAO"].PTTT01],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT02. Danh sách bệnh nhân thông qua mổ",
    i18n: "baoCao.pttt02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-02",
    accessRoles: [ROLES["BAO_CAO"].PTTT02],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT03. Báo cáo thống kê phẫu thuật, thủ thuật",
    i18n: "baoCao.pttt03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-03",
    accessRoles: [ROLES["BAO_CAO"].PTTT03],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04. Bảng kê thanh toán phụ cấp Phẫu thuật - Thủ thuật",
    i18n: "baoCao.pttt04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04",
    accessRoles: [ROLES["BAO_CAO"].PTTT04],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title:
      "PTTT04.1. Danh sách phát tiền phụ cấp Phẫu thuật - Thủ thuật (theo chuyên khoa)",
    i18n: "baoCao.pttt04_1",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_1",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_01],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.2. Bảng kê thanh toán Phẫu thuật - Thủ thuật (Theo dịch vụ)",
    i18n: "baoCao.pttt04_2",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_2",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_02],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.3. Bảng tổng hợp loại phẫu thuật, thủ thuật",
    i18n: "baoCao.pttt04_3",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_3",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_03],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.4. Bảng tổng hợp chi tiền phẫu thuật theo ngày",
    i18n: "baoCao.pttt04_4",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_4",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_04],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.5. Bảng tổng hợp thanh toán phẫu thuật theo yêu cầu",
    i18n: "baoCao.pttt04_5",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_5",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_05],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.7. Báo cáo Phụ cấp theo nhân viên",
    i18n: "baoCao.pttt04_7",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_7",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_07],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.8. Báo cáo Phụ cấp mổ, đẻ trọn gói",
    i18n: "baoCao.pttt04_8",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_8",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_08],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT04.9. Bảng thanh toán tiền Phẫu thuật - Thủ thuật",
    i19n: "baoCao.pttt04_9",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-04_9",
    accessRoles: [ROLES["BAO_CAO"].PTTT04_09],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT05. Danh sách chi trả tiền công cho BS tham gia PTTT",
    i18n: "baoCao.pttt05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-05",
    accessRoles: [ROLES["BAO_CAO"].PTTT05],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT06. Báo cáo thanh toán chi trả BS theo gói yêu cầu",
    i18n: "baoCao.pttt06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-06",
    accessRoles: [ROLES["BAO_CAO"].PTTT06],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title:
      "PTTT07. Báo cáo tổng hợp chi tiền phẫu thuật thủ thuật theo khoa thực hiện",
    i18n: "baoCao.pttt07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-07",
    accessRoles: [ROLES["BAO_CAO"].PTTT07],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT08. Báo cáo PTTT theo yêu cầu",
    i18n: "baoCao.pttt08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-08",
    accessRoles: [ROLES["BAO_CAO"].PTTT08],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "PTTT09. Sổ bàn giao người bệnh về phòng hậu phẫu",
    i18n: "baoCao.pttt09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/pttt-09",
    accessRoles: [ROLES["BAO_CAO"].PTTT09],
    group: NHOM_BAO_CAO.BAO_CAO_PHAU_THUAT_THU_THUAT,
    capitalizeTitle: false,
  },
  {
    title: "DD01. BÀN GIAO, KÝ NHẬN SUẤT ĂN",
    i18n: "baoCao.dd01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/dd-01",
    accessRoles: [ROLES["BAO_CAO"].DD01],
    group: NHOM_BAO_CAO.BAO_CAO_SUAT_AN,
    capitalizeTitle: false,
  },
  {
    title: "DD03. Bảng tổng hợp thanh toán suất ăn",
    i18n: "baoCao.dd03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/dd-03",
    accessRoles: [ROLES["BAO_CAO"].DD03],
    group: NHOM_BAO_CAO.BAO_CAO_SUAT_AN,
    capitalizeTitle: false,
  },
  {
    title: "TIEM_01. Báo cáo tổng hợp xuất vắc xin",
    i18n: "baoCao.tiem01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tiem-01",
    accessRoles: [ROLES["BAO_CAO"].TIEM_01],
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
    capitalizeTitle: false,
  },
  {
    title: "TIEM_02. Báo cáo thống kê số liệu tiêm chủng",
    i18n: "baoCao.tiem02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tiem-02",
    accessRoles: [ROLES["BAO_CAO"].TIEM_02],
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
    capitalizeTitle: false,
  },
  {
    title: "TIEM03. Báo cáo tiêm ngừa",
    i18n: "baoCao.tiem03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tiem-03",
    accessRoles: [ROLES["BAO_CAO"].TIEM_03],
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
    capitalizeTitle: false,
  },
  {
    title: "TIEM04. Báo cáo tình hình sử dụng vắc xin tiêm chủng dịch vụ",
    i18n: "baoCao.tiem04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tiem-04",
    accessRoles: [ROLES["BAO_CAO"].TIEM_04],
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
    capitalizeTitle: false,
  },
  {
    title: "TIEM05. Báo cáo kết quả tiêm chủng dịch vụ",
    i18n: "baoCao.tiem05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/tiem-05",
    accessRoles: [ROLES["BAO_CAO"].TIEM_05],
    group: NHOM_BAO_CAO.BAO_CAO_TIEM_CHUNG,
    capitalizeTitle: false,
  },
  {
    title: "KSNK_01. Danh sách nhiễm khuẩn bệnh viện",
    i18n: "baoCao.ksnk_01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksnk_01",
    accessRoles: [ROLES["BAO_CAO"].KSNK_01],
    group: NHOM_BAO_CAO.BAO_CAO_KSNK,
    capitalizeTitle: false,
  },
  {
    title: "KSNK_01. Danh sách nhiễm khuẩn bệnh viện",
    i18n: "baoCao.ksnk_02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ksnk_02",
    accessRoles: [ROLES["BAO_CAO"].KSNK_02],
    group: NHOM_BAO_CAO.BAO_CAO_KSNK,
    capitalizeTitle: false,
  },
  {
    title: "LAO01. Báo cáo tình hình đăng kí điều trị bệnh nhân lao",
    i18n: "baoCao.lao01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/lao01",
    accessRoles: [ROLES["BAO_CAO"].LAO01],
    group: NHOM_BAO_CAO.BAO_CAO_LAO,
    capitalizeTitle: false,
  },
  {
    title: "KDD01. Báo cáo nhập kho tổng hợp chế phẩm dinh dưỡng",
    i18n: "baoCao.kdd01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd01",
    accessRoles: [ROLES["BAO_CAO"].KDD01],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD02. Báo cáo CPDD theo đơn vị sử dụng",
    i18n: "baoCao.kdd02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd02",
    accessRoles: [ROLES["BAO_CAO"].KDD02],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD03. Bảng tổng hợp thanh toán CPDD theo ngày thực hiện",
    i18n: "baoCao.kdd03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd03",
    accessRoles: [ROLES["BAO_CAO"].KDD03],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD04. Báo cáo xuất kho CPDD theo ngày",
    i18n: "baoCao.kdd04",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd04",
    accessRoles: [ROLES["BAO_CAO"].KDD04],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD05. Báo cáo xuất kho CPDD theo tháng",
    i18n: "baoCao.kdd05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd05",
    accessRoles: [ROLES["BAO_CAO"].KDD05],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD10. Báo cáo phát chế phẩm dinh dưỡng theo ngày",
    i18n: "baoCao.kdd10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd10",
    accessRoles: [ROLES["BAO_CAO"].KDD10],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD09. Báo cáo chế phẩm dinh dưỡng theo người kê",
    i18n: "baoCao.kdd09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd09",
    accessRoles: [ROLES["BAO_CAO"].KDD09],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD11. Báo cáo bàn giao, ký nhận chế phẩm dinh dưỡng",
    i18n: "baoCao.kdd11",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd11",
    accessRoles: [ROLES["BAO_CAO"].KDD11],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD12. Báo cáo bàn giao, ký nhận chế phẩm dinh dưỡng",
    i18n: "baoCao.kdd12",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd12",
    accessRoles: [ROLES["BAO_CAO"].KDD12],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD07. Báo cáo xuất nhập tồn tổng hợp",
    i18n: "baoCao.kdd07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd07",
    accessRoles: [ROLES["BAO_CAO"].KDD07],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD08. Bảng tổng hợp thanh toán CPDD theo thời gian thanh toán",
    i18n: "baoCao.kdd08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd08",
    accessRoles: [ROLES["BAO_CAO"].KDD08],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD13. Báo cáo nhập chế phẩm dinh dưỡng từ các khoa trả lại",
    i18n: "baoCao.kdd13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd13",
    accessRoles: [ROLES["BAO_CAO"].KDD13],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD14. Báo cáo thanh toán chi tiết chế phẩm dinh dưỡng",
    i18n: "baoCao.kdd14",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd14",
    accessRoles: [ROLES["BAO_CAO"].KDD14],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
    capitalizeTitle: false,
  },
  {
    title: "KDD06. Báo cáo xuất chế phẩm dinh dưỡng theo khoa",
    i18n: "baoCao.kdd06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/kdd06",
    accessRoles: [ROLES["BAO_CAO"].KDD06],
    group: NHOM_BAO_CAO.BAO_CAO_KHO_DINH_DUONG,
  },
  {
    title: "QT01. Báo cáo tra cứu log xoá dịch vụ của người bệnh",
    i18n: "baoCao.qt01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/qt01",
    accessRoles: [ROLES["BAO_CAO"].QT01],
    group: NHOM_BAO_CAO.BAO_CAO_QT,
    capitalizeTitle: false,
  },
  {
    title: "QT02. Báo cáo tra cứu log thay đổi dữ liệu của người bệnh",
    i18n: "baoCao.qt02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/qt02",
    accessRoles: [ROLES["BAO_CAO"].QT02],
    group: NHOM_BAO_CAO.BAO_CAO_QT,
    capitalizeTitle: false,
  },
  {
    title: "QT03. Báo cáo thời gian sử dụng phần mềm của nhân viên",
    i18n: "baoCao.qt03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/qt03",
    accessRoles: [ROLES["BAO_CAO"].QT03],
    group: NHOM_BAO_CAO.BAO_CAO_QT,
    capitalizeTitle: false,
  },
  {
    title: "QTBH13. Báo cáo danh sách NB chưa tạo hồ sơ quyết toán",
    i18n: "baoCao.qtbh13",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/qtbh13",
    accessRoles: [ROLES["BAO_CAO"].QTBH13],
    group: NHOM_BAO_CAO.BAO_CAO_QUYET_TOAN_BAO_HIEM,
    capitalizeTitle: false,
  },
  {
    title: "DDLS05. Báo cáo bệnh nhân sử dụng thuốc kháng sinh trên 10 ngày",
    i18n: "baoCao.ddls05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls05",
    accessRoles: [ROLES["BAO_CAO"].DDLS05],
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
    capitalizeTitle: false,
  },
  {
    title: "DDLS06. Báo cáo Dược lâm sàng nội trú/ngoại trú",
    i18n: "baoCao.ddls06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls06",
    accessRoles: [ROLES["BAO_CAO"].DDLS06],
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
    capitalizeTitle: false,
  },
  {
    title: "DDLS07. Báo cáo Phân tích ABC/VEN",
    i18n: "baoCao.ddls07",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls07",
    accessRoles: [ROLES["BAO_CAO"].DDLS07],
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
    capitalizeTitle: false,
  },
  {
    title: "DDLS08. Báo cáo Ngày sử dụng thuốc kháng sinh của người bệnh",
    i18n: "baoCao.ddls08",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls08",
    accessRoles: [ROLES["BAO_CAO"].DDLS08],
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
    capitalizeTitle: false,
  },
  {
    title: "DDLS09. Báo cáo tiền thuốc trung bình theo số ngày điều trị",
    i18n: "baoCao.ddls09",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls09",
    accessRoles: [ROLES["BAO_CAO"].DDLS09],
    group: NHOM_BAO_CAO.BAO_CAO_DDLS,
    capitalizeTitle: false,
  },
  {
    title:
      "DDLS10. Báo cáo tổng tiền thuốc trung bình của NB tại khoa/cả đợt điều trị",
    i18n: "baoCao.ddls10",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/ddls10",
    accessRoles: [ROLES["BAO_CAO"].DDLS10],
  },
  {
    title: "SangLocDD01. Báo cáo ngày sử dụng thuốc kháng sinh của người bệnh",
    i18n: "baoCao.sldd01",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/sldd01",
    accessRoles: [ROLES["BAO_CAO"].SLDD01],
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
    capitalizeTitle: false,
  },
  {
    title:
      "SLDD02. Báo cáo kết quả đánh giá dinh dưỡng trên Người bệnh lúc nhập viện",
    i18n: "baoCao.sldd02",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/sldd02",
    accessRoles: [ROLES["BAO_CAO"].SLDD02],
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
    capitalizeTitle: false,
  },
  {
    title: "SLDD03. Báo cáo tỉ lệ đánh giá dinh dưỡng Người bệnh khi Nhập viện",
    i18n: "baoCao.sldd03",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/sldd03",
    accessRoles: [ROLES["BAO_CAO"].SLDD03],
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
    capitalizeTitle: false,
  },
  {
    title:
      "SLDD05. Báo cáo kết quả sàng lọc, đánh giá và can thiệp dinh dưỡng trên bệnh nhân nội trú",
    i18n: "baoCao.sldd05",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/sldd05",
    accessRoles: [ROLES["BAO_CAO"].SLDD05],
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
    capitalizeTitle: false,
  },
  {
    title: "SLDD06. Báo cáo Chi tiết Người bệnh được sàng lọc dinh dưỡng",
    i18n: "baoCao.sldd06",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/bao-cao/sldd06",
    accessRoles: [ROLES["BAO_CAO"].SLDD06],
    group: NHOM_BAO_CAO.BAO_CAO_SANG_LOC_DD,
    capitalizeTitle: false,
  },
];

export const ListTiepDon = [
  {
    title: "Tiếp đón",
    i18n: "tiepDon.tiepDon",
    icon: <SVG.IcTiepDon1 />,
    link: "/tiep-don",
  },
  {
    i18n: "tiepDon.danhSachNguoiBenhDaTiepDon",
    title: "Danh sách người bệnh đã tiếp đón",
    icon: <SVG.IcDanhSachNbDaTiepDon1 />,
    link: "/quan-ly-tiep-don/danh-sach-nb-da-tiep-don",
  },
  {
    i18n: "tiepDon.danhSachNguoiBenhHuyTiepDon",
    title: "Danh sách người bệnh hủy tiếp đón",
    icon: <SVG.IcDanhSachNbHuyTiepDon />,
    link: "/quan-ly-tiep-don/danh-sach-nb-huy-tiep-don",
  },
  {
    i18n: "tiepDon.danhSachLichHen",
    mainTitle: "title.danhSachLichHenTiepDon",
    title: "Danh sách lịch hẹn",
    icon: <SVG.IcDanhSachLichHen />,
    link: "/quan-ly-tiep-don/danh-sach-lich-hen",
  },
];

export const ListTiemChung = [
  {
    title: "Tiếp đón tiêm chủng",
    i18n: "tiemChung.tiepDon",
    icon: <SVG.IcTiepDonTiemChung />,
    link: "/quan-ly-tiem-chung/tiep-don-tiem-chung",
    accessRoles: [ROLES["QUAN_LY_TIEM_CHUNG"].TIEP_DON_TIEM_CHUNG],
  },
  {
    title: "Danh sách tiếp đón tiêm chủng",
    i18n: "tiemChung.dsTiepDonTiemChung",
    icon: <SVG.IcDanhSachTiepDonTiemChung />,
    link: "/quan-ly-tiem-chung/danh-sach-tiem-chung",
    accessRoles: [ROLES["QUAN_LY_TIEM_CHUNG"].DANH_SACH_TIEM_CHUNG],
  },
  {
    title: "Danh sách khám sàng lọc",
    i18n: "tiemChung.dsKhamSL",
    icon: <SVG.IcDanhSachKhamSangLoc />,
    link: "/quan-ly-tiem-chung/danh-sach-kham-sang-loc",
  },
  {
    title: "Danh sách tiêm",
    i18n: "tiemChung.danhSachTiem",
    icon: <SVG.IcDanhSachTiem />,
    link: "/quan-ly-tiem-chung/danh-sach-tiem",
  },
  {
    title: "Danh sách theo dõi sau tiêm",
    i18n: "tiemChung.dsTheoDoiSauTiem",
    icon: <SVG.IcDanhSachTheoDoiSauTiem />,
    link: "/quan-ly-tiem-chung/danh-sach-theo-doi-sau-tiem",
    accessRoles: [ROLES["QUAN_LY_TIEM_CHUNG"].DANH_SACH_THEO_DOI_SAU_TIEM],
  },
  {
    title: "Danh sách lịch hẹn",
    i18n: "tiepDon.danhSachLichHen",
    mainTitle: "title.danhSachLichHenTiemChung",
    icon: <SVG.IcDanhSachLichHen />,
    link: "/quan-ly-tiem-chung/danh-sach-lich-hen",
  },
  {
    title: "Danh sách vắc xin đẩy cổng TCQG",
    i18n: "tiemChung.danhSachVacxinDayCongTcqg",
    icon: <SVG.IcDanhSachVaccinDayCongTcqg />,
    link: "/quan-ly-tiem-chung/danh-sach-day-cong-tcqg",
  },
];

export const ListQuanLyNoiTru = [
  {
    title: "Danh sách lập bệnh án",
    i18n: "lapBenhAn.danhSachLapBenhAn",
    mainTitle: "title.danhSachLapBenhAnNoiTru",
    icon: <SVG.IcDanhSachLapBenhAn />,
    link: "/quan-ly-noi-tru/danh-sach-lap-benh-an?trangThai=10",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DANH_SACH_LAP_BENH_AN],
  },
  {
    title: "Danh sách người bệnh nội trú",
    i18n: "quanLyNoiTru.danhSachNguoiBenhNoiTru",
    icon: <SVG.IcDanhSachNbNoiTru />,
    link: "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DANH_SACH_NB_NOI_TRU],
  },
  {
    title: "Gia hạn thẻ, chuyển đối tượng",
    i18n: "quanLyNoiTru.giaHanThe.giaHanTheChuyenDoiTuong",
    icon: <SVG.IcGiaHanTheChuyenDoiTuong />,
    link: "/quan-ly-noi-tru/gia-han-the-chuyen-doi-tuong",
    accessRoles: [
      ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_GIA_HAN_THE_VA_CAP_NHAT_DOI_TUONG,
    ],
  },
  {
    title: "Quản lý điều dưỡng phụ trách phòng giường",
    i18n: "quanLyNoiTru.quanLyDieuDuongPhuTrachPhongGiuong",
    icon: <SVG.IcQuanLyDieuDuongPhuTrachPhongGiuong />,
    link: "/quan-ly-noi-tru/quan-ly-dieu-duong-phu-trach-phong-giuong",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].QUAN_LY_DIEU_DUONG_PHU_TRACH_GIUONG],
  },
  {
    title: "Danh sách theo dõi chuyển dạ",
    i18n: "quanLyNoiTru.danhSachTheoDoiChuyenDa",
    icon: <SVG.IcQuanLyDieuDuongPhuTrachPhongGiuong />,
    link: "/quan-ly-noi-tru/danh-sach-theo-doi-chuyen-da",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].QUAN_LY_CHUYEN_DA],
  },
  {
    title: "Danh sách phiếu lĩnh",
    i18n: "quanLyNoiTru.danhSachPhieuLinh",
    icon: <SVG.IcDanhSachPhieuLinh />,
    link: "/quan-ly-noi-tru/danh-sach-phieu-linh",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DANH_SACH_PHIEU_LINH],
  },
  {
    title: "Danh sách đơn thuốc ra viện",
    i18n: "quanLyNoiTru.danhSachDonThuocRaVien.title",
    icon: <SVG.IcDanhSachPhieuLinh />,
    link: "/quan-ly-noi-tru/danh-sach-don-thuoc-ra-vien",
    accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DS_DON_THUOC_RA_VIEN],
  },
];

export const ListDieuTriDaiHan = [
  {
    title: "Danh sách người bệnh điều trị dài hạn",
    i18n: "dieuTriDaiHan.danhSachNBDieuTriDaiHan",
    icon: <SVG.IcDieuTriDaiHan />,
    link: "/dieu-tri-dai-han/danh-sach-nb-dieu-tri-dai-han",
    accessRoles: [ROLES["DIEU_TRI_DAI_HAN"].DANH_SACH_NB_DIEU_TRI_DAI_HAN],
  },
  {
    title: "Danh sách lập bệnh án",
    mainTitle: "title.danhSachLapBenhAnDieuTriDaiHan",
    i18n: "lapBenhAn.danhSachLapBenhAn",
    icon: <SVG.IcDanhSachLapBenhAn />,
    link: "/dieu-tri-dai-han/danh-sach-lap-benh-an?trangThai=10",
    accessRoles: [ROLES["DIEU_TRI_DAI_HAN"].DANH_SACH_LAP_BENH_AN],
  },
];

export const ListKhamSucKhoe = [
  {
    title: "Danh sách phiếu báo giá",
    i18n: "khamSucKhoe.danhSachPhieuBaoGia",
    icon: <SVG.IcDanhSachPhieuBaoGia />,
    link: "/kham-suc-khoe/phieu-bao-gia",
    accessRoles: [ROLES["KHAM_SUC_KHOE"].XEM_DS_PHIEU_BAO_GIA],
  },
  {
    title: "Danh sách hợp đồng",
    i18n: "khamSucKhoe.danhSachHopDong",
    icon: <SVG.IcDanhSachHopDong />,
    link: "/kham-suc-khoe/hop-dong",
    accessRoles: [ROLES["KHAM_SUC_KHOE"].XEM_DS_HOP_DONG],
  },
];

export const ListSinhHieu = [
  {
    title: "Danh sách sinh hiệu",
    i18n: "sinhHieu.danhSachSinhHieu",
    icon: <SVG.IcSinhHieu />,
    link: "/sinh-hieu/ds-sinh-hieu",
    accessRoles: [ROLES["SINH_HIEU"].DS_SINH_HIEU],
  },
];

export const ListNhaThuoc = [
  {
    title: "Danh sách đơn thuốc",
    i18n: "nhaThuoc.danhSachDonThuoc",
    icon: <SVG.IcDanhSachDonThuoc />,
    link: "/nha-thuoc",
  },
  {
    title: "Liên thông GPP",
    i18n: "nhaThuoc.lienThongGPP",
    icon: <SVG.IcLienThongGpp />,
    link: "/nha-thuoc/lien-thong-gpp",
    accessRoles: [ROLES["NHA_THUOC"].XEM_MAN_HINH_LIEN_THONG_GPP],
  },
];

export const ListGoiDichVu = [
  {
    title: "Danh sách người bệnh sử dụng gói",
    i18n: "goiDichVu.danhSachNguoiBenhSuDungGoi",
    icon: <SVG.IcDanhSachNbSuDungGoi />,
    link: "/goi-dich-vu/danh-sach-su-dung-goi",
    accessRoles: [ROLES["GOI_DICH_VU"].DANH_SACH_NB_SU_DUNG_GOI_DICH_VU],
  },
  {
    title: "Danh mục gói dịch vụ",
    i18n: "danhMuc.danhMucGoiDichVu",
    icon: <SVG.IcDanhMucGoiDichVu />,
    link: "/goi-dich-vu/danh-muc",
    accessRoles: [ROLES["GOI_DICH_VU"].GOI_DICH_VU],
  },
];

export const ListPhauThuatThuThuat = [
  {
    title: "Danh sách phẫu thuật- thủ thuật",
    i18n: "pttt.danhSachPhauThuatThuThuat",
    icon: <SVG.IcPhauThuatThuThuat />,
    link: "/phau-thuat-thu-thuat/danh-sach-nguoi-benh",
  },
];

export const ListGiayDayCong = [
  {
    i18n: "giayDayCong.dsGiayNghiHuong",
    icon: <SVG.IcDanhSachGiayNghiHuong />,
    link: "/giay-day-cong/giay-nghi-huong",
  },
  {
    i18n: "giayDayCong.danhSachNbTuVong",
    icon: <SVG.IcDanhSachNbTuVong />,
    link: "/giay-day-cong/nb-tu-vong",
  },
  {
    i18n: "giayDayCong.danhSachNbRaVien",
    icon: <SVG.IcDanhSachNbRaVien />,
    link: "/giay-day-cong/nb-ra-vien",
  },
  {
    i18n: "giayDayCong.dsPhieuTomTatBa",
    icon: <SVG.IcDanhSachPhieuTomTatBenhAn />,
    link: "/giay-day-cong/phieu-tom-tat-ba",
  },
  {
    i18n: "giayDayCong.danhSachGiayChungSinh",
    icon: <SVG.IcDanhSachPhieuChungSinh />,
    link: "/giay-day-cong/giay-chung-sinh",
  },
  {
    i18n: "giayDayCong.danhSachGiayKskLaiXe",
    icon: <SVG.IcDanhSachGiayKskLaiXe />,
    link: "/giay-day-cong/giay-ksk-lai-xe",
  },
  {
    i18n: "giayDayCong.danhSachGiayChungNhanNghiDuongThai",
    icon: <SVG.IcDanhSachGiayChungNhanNghiDuongThai />,
    link: "/giay-day-cong/giay-chung-nhan-nghi-duong-thai",
  },
];

export const ListDashboard = [
  {
    i18n: "dashboard.tongQuanBenhVien",
    icon: <SVG.IcTongQuanBenhVien />,
    link: "/dashboard/so-lieu-benh-vien",
    accessRoles: [ROLES["DASHBOARD"].TV.SO_LIEU_BENH_VIEN],
  },
  {
    i18n: "dashboard.tongQuanNgoaiTru",
    icon: <SVG.IcTongQuanNgoaiTru />,
    link: "/dashboard/so-lieu-ngoai-tru",
    accessRoles: [ROLES["DASHBOARD"].TV.SO_LIEU_NGOAI_TRU],
  },
  {
    i18n: "dashboard.tongQuanDoanhThu",
    icon: <SVG.IcTongQuanDoanhThu />,
    link: "/dashboard/so-lieu-doanh-thu",
    accessRoles: [ROLES["DASHBOARD"].TV.SO_LIEU_DOANH_THU],
  },
  {
    i18n: "dashboard.tongQuanGiuongPhong",
    icon: <SVG.IcTongQuanGiuongPhong />,
    link: "/dashboard/so-lieu-giuong-phong",
    accessRoles: [ROLES["DASHBOARD"].TV.SO_LIEU_GIUONG_PHONG],
  },
  {
    i18n: "dashboard.thoiGianChoKham",
    icon: <SVG.IcThoiGianChoKham />,
    link: "/dashboard/thoi-gian-cho-kham",
    accessRoles: [ROLES["DASHBOARD"].TV.THOI_GIAN_CHO_KHAM],
  },
];

export const ListPhucHoiChucNang = [
  {
    render: ({ render, ...config }) => {
      return {
        ...generatePhcnYhctTitle([
          {
            key: "dieuTriKetHop",
            i18n: "phcn.dsDieuTriKetHop",
            title: "Danh sách điều trị kết hợp",
          },
          {
            key: "dieuTriYhct",
            i18n: "phcn.dsDieuTriYhct",
            title: "Danh sách điều trị YHCT",
          },
          {
            key: "dieuTriPhcn",
            i18n: "phcn.dsDieuTriPhcn",
            title: "Danh sách điều trị phục hồi chức năng",
          },
          {
            key: "thanNhanTao",
            i18n: "phcn.dsDieuTriThanNhanTao",
            title: "Danh sách điều trị thận nhân tạo",
          },
          {
            key: "banChan",
            i18n: "phcn.dsDieuTriBanChan",
            title: "Danh sách điều trị bàn chân",
          },
          {
            key: "hoaXaTri",
            i18n: "phcn.dsDieuTriHoaXaTri",
            title: "Danh sách điều trị hóa xạ trị",
          },
        ]),
        ...config,
      };
    },
    icon: <SVG.IcPhucHoiChucNang />,
    link: "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn",
    accessRoles: [
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_PHCN,
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_YHCT,
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_THAN_NHAN_TAO,
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_BAN_CHAN,
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_HOA_XA_TRI,
    ],
  },
];

export const ListKeHoachTongHop = [
  {
    title: "Danh sách lưu trữ bệnh án",
    i18n: "hsba.danhSachLuuTruBa",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-luu-tru-benh-an",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].DANH_SACH_LUU_TRU_BENH_AN],
  },
  {
    title: "Danh sách duyệt BH",
    i18n: "khth.danhSachDuyetBh",
    icon: <SVG.IcDanhSachDuyetBaoHiem />,
    link: "/ke-hoach-tong-hop/danh-sach-duyet-bao-hiem?trangThaiDuyetBh=10&dsTrangThai=100,110",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].DUYET_BAO_HIEM],
  },
  {
    title: "Danh sách hồ sơ giám định bảo hiểm trực tiếp",
    i18n: "hsba.danhSachHSGiamDinhBHTrucTiep",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-hs-giam-dinh-bh",
    accessRoles: [],
  },
  {
    title: "Danh sách phiếu mượn bệnh án",
    i18n: "khth.dsPhieuMuonBA",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-phieu-muon-benh-an",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].HIEN_THI_DS_PHIEU_MUON_BA],
  },
  {
    title: "Danh sách phiếu trả bệnh án",
    i18n: "khth.phieuTra.dsPhieuTraBA",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-phieu-tra-benh-an",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].HIEN_THI_DS_PHIEU_TRA_BA],
  },
  {
    title: "Danh sách bệnh án mượn",
    i18n: "khth.dsBAMuon",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-benh-an-muon",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].DANH_SACH_BA_MUON],
  },
  {
    title: "Danh sách báo cáo tháng theo khoa",
    i18n: "khth.danhSachBaoCaoThangTheoKhoa",
    icon: <SVG.IcDanhSachLuuTruBenhAn />,
    link: "/ke-hoach-tong-hop/danh-sach-bao-cao-thang-theo-khoa",
    accessRoles: [ROLES["KE_HOACH_TONG_HOP"].HIEN_THI_BAO_CAO_THANG_KHOA],
  },
];

export const ListTroGiup = [
  {
    title: "Tài liệu hướng dẫn sử dụng",
    i18n: "danhMuc.danhMucTaiLieuHDSD",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/tro-giup/tro-giup-hdsd",
    accessRoles: [],
  },
];

// export const ListQms = [
//   {
//     title: "Kiosk lấy số",
//     i18n: "common.KIOSK",
//     link: "/qms/thiet-lap-ngang",
//     accessRoles: [ROLES["HE_THONG"].QMS],
//   },
// ];

export const ListKiosk = [
  {
    i18n: "kiosk.kioskLaySoTiepDon",
    link: "/kiosk/chon-khu-vuc",
    icon: <SVG.IcKiosk />,
    accessRoles: [ROLES["KIOSK"].LAY_SO_TIEP_DON],
  },
  {
    i18n: "kiosk.kioskLaySoThuNgan",
    link: "/kiosk/lay-so-thu-ngan",
    icon: <SVG.IcKioskLaySoThuNgan />,
    accessRoles: [ROLES["KIOSK"].LAY_SO_THU_NGAN],
  },
  {
    i18n: "kiosk.kioskQuetMaQR",
    link: "/kiosk/quet-qr",
    icon: <SVG.IcKioskQuetMaQr />,
    accessRoles: [ROLES["KIOSK"].QUET_MA_QR],
  },
  {
    i18n: "kiosk.kioskQuetVaGoiNBVaoQuay",
    link: "/kiosk/lay-so-tong-hop",
    icon: <SVG.IcKioskQuetVaGoiNbVaoQuay />,
    accessRoles: [ROLES["KIOSK"].QUET_GOI_NB_VAO_QUAY],
  },
  {
    i18n: "kiosk.kioskTiepDonDienThongTinNB",
    link: "/kiosk/lua-chon-hinh-thuc-kham",
    icon: <SVG.IcKiosk />,
    accessRoles: [ROLES["KIOSK"].TIEP_DON_DIEN_THONG_TIN_NB],
  },
  {
    i18n: "kiosk.kioskLaySttTheoLoaiDoiTuong",
    link: "/kiosk/lay-so-thu-tu-theo-loai-doi-tuong",
    icon: <SVG.IcKiosk />,
    accessRoles: [ROLES["KIOSK"].LAY_SO_THU_TU_THEO_LOAI_DOI_TUONG],
  },
];

export const ListHoiChan = [
  {
    title: "Danh sách người bệnh hội chẩn",
    i18n: "hoiChan.dsNguoiBenhHoiChan",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/hoi-chan",
    accessRoles: [ROLES["HOI_CHAN"].DANH_SACH_HOI_CHAN],
  },
];

export const ListLienThongDonThuocDienTu = [
  {
    title: "Danh sách liên thông đơn thuốc điện tử",
    i18n: "lienThongDonThuoc.danhSachLienThongDonThuocDienTu",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/lien-thong-don-thuoc-dien-tu",
    accessRoles: [ROLES["LIEN_THONG_DON_THUOC_DIEN_TU"].XEM_DS_LIEN_THONG],
  },
];

export const ListKiemSoatNhiemKhuan = [
  {
    title: "Danh sách người bệnh nhiễm khuẩn",
    i18n: "kiemSoatNhiemKhuan.danhSachNguoiBenhNhiemKhuan",
    icon: <SVG.IcNhiemKhuan />,
    link: "/kiem-soat-nhiem-khuan/danh-sach",
    accessRoles: [ROLES["KIEM_SOAT_NHIEM_KHUAN"].KIEM_SOAT_NHIEM_KHUAN],
  },
];

export const ListPhacDoDieuTri = [
  {
    title: "Danh mục mẫu phác đồ điều trị",
    i18n: "phacDoDieuTri.danhMucMauPhacDoDieuTri",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/phac-do-dieu-tri/danh-muc-mau",
    accessRoles: [ROLES["PHAC_DO_DIEU_TRI"].XEM_DANH_MUC_PHAC_DO_DIEU_TRI],
  },
  {
    title: "Danh sách NB áp dụng phác đồ điều trị",
    i18n: "phacDoDieuTri.danhSachNbApDungPhacDoDieuTri",
    icon: <SVG.IcPhacDoDieuTri1 />,
    link: "/phac-do-dieu-tri/danh-sach-nb-ap-dung-phac-do-dieu-tri",
    accessRoles: [
      ROLES["PHAC_DO_DIEU_TRI"].XEM_DANH_SACH_AP_DUNG_PHAC_DO_DIEU_TRI,
    ],
  },
];

export const ListDoThiLuc = [
  {
    title: "Danh sách đo thị lực",
    i18n: "doThiLuc.danhSachDoThiLuc",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/do-thi-luc/danh-sach",
    accessRoles: [ROLES["DO_THI_LUC"].DANH_SACH_DO_THI_LUC],
  },
];

export const ListQuanLyBaoCaoAdr = [
  {
    title: "Danh sách báo cáo ADR",
    i18n: "quanLyBaoCaoAdr.danhSachBaoCaoAdr",
    icon: require("assets/images/pagehome/icChucNang1.png"),
    link: "/quan-ly-bao-cao-adr/ds-bao-cao-adr",
    accessRoles: [],
  },
];

export const ListKpis = [
  {
    title: "Chỉ số KPIs",
    i18n: "kpis.chiSoKpis",
    icon: <SVG.IcKPIs />,
    link: "/kpis/chi-so-kpis",
    accessRoles: [ROLES["KPIS"].KPIS],
  },
];

export const ListQuanLyDieuTriLao = [
  {
    title: "Danh sách người bệnh đăng ký thuốc lao",
    i18n: "quanLyDieuTriLao.danhSachNguoiBenhDangKyThuocLao",
    icon: <SVG.IcDangKyThuocLao />,
    link: "/quan-ly-dieu-tri-lao/danh-sach-nb-dang-ky-thuoc-lao",
    accessRoles: [ROLES["QUAN_LY_DIEU_TRI_LAO"].DANH_SACH_NB_DANG_KY_THUOC_LAO],
  },
  {
    title: "Danh sách người bệnh điều trị lao",
    i18n: "quanLyDieuTriLao.danhSachNguoiBenhDieuTriLao",
    icon: <SVG.IcNbDieuTriLao />,
    link: "/quan-ly-dieu-tri-lao/danh-sach-nb-dieu-tri-lao",
    accessRoles: [],
  },
];

export const ListHenNoiSoi = [
  {
    title: "Danh mục thời gian hẹn nội soi",
    i18n: "henNoiSoi.dmThoiGianHenNoiSoi",
    icon: <SVG.IcThoiGianHenNoiSoi />,
    link: "/hen-noi-soi/dm-thoi-gian-hen-noi-soi",
    accessRoles: [ROLES["HEN_NOI_SOI"].XEM_THOI_GIAN_HEN_NOI_SOI],
  },
  {
    title: "Danh mục xét nghiệm hẹn nội soi",
    i18n: "henNoiSoi.dmXetNghiemHenNoiSoi",
    icon: <SVG.IcModuleHenNoiSoi />,
    link: "/hen-noi-soi/dm-xet-nghiem-hen-noi-soi",
    accessRoles: [ROLES["HEN_NOI_SOI"].XEM_DV_XET_NGHIEM_NOI_SOI],
  },
  {
    title: "Danh sách hẹn nội soi, sinh thiết",
    i18n: "henNoiSoi.dsHenNoiSoiSinhThiet",
    icon: <SVG.IcDsHenNoiSoiSinhThiet />,
    link: "/hen-noi-soi/ds-hen-noi-soi-sinh-thiet",
    accessRoles: [ROLES["HEN_NOI_SOI"].XEM_DS_LICH_HEN],
  },
];

export const ListQuanLyNhanLuc = [
  {
    title: "Danh sách quản lý nhân lực hàng ngày",
    i18n: "quanLyNhanLuc.danhSachQuanLyNhanLucHangNgay",
    icon: <SVG.IcQuanLyNhanLuc />,
    link: "/quan-ly-nhan-luc/danh-sach-quan-ly-nhan-luc-hang-ngay",
    accessRoles: [ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC],
  },
];

export const ListQuanLyHoaHong = [
  {
    title: "Danh mục chính sách hoa hồng",
    i18n: "danhMuc.danhMucChinhSachHoaHong",
    icon: "/media/img/ic-menu.png",
    link: "/danh-muc/chinh-sach-hoa-hong",
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    title: "Danh mục Nhóm dịch vụ hoa hồng",
    i18n: "danhMuc.danhMucNhomDichVuHoaHong",
    icon: "/media/img/ic-menu.png",
    link: "/danh-muc/nhom-dich-vu-hoa-hong",
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    title: "Danh mục Đối tác hoa hồng",
    i18n: "danhMuc.danhMucDoiTacHoaHong",
    icon: "/media/img/ic-menu.png",
    link: "/danh-muc/doi-tac-hoa-hong",
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    title: "Danh mục Dịch vụ hoa hồng",
    i18n: "danhMuc.danhMucDichVuHoaHong",
    icon: "/media/img/ic-menu.png",
    link: "/danh-muc/dich-vu-hoa-hong",
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
  },
  {
    title: "Tính tiền hoa hồng",
    i18n: "hoaHong.tinhTienHoaHong",
    icon: "/media/img/com-tool.png",
    link: "/quan-ly-hoa-hong/tinh-tien-hoa-hong",
    accessRoles: [ROLES["COM"].MODULE],
  },
];

export const ListQuanLyYeuCau = [
  {
    title: "Danh sách yêu cầu",
    i18n: "quanLyYeuCau.danhSachYeuCau",
    icon: <SVG.IcQuanLyYeuCau />,
    link: "/quan-ly-yeu-cau/danh-sach-yeu-cau",
    accessRoles: [ROLES["QUAN_LY_YEU_CAU"].XEM_DS_QUAN_LY_YEU_CAU],
  },
];
