import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage } from "./styled";
import { useTranslation } from "react-i18next";
import { Col, Menu, Row } from "antd";
import PhieuXuatPhaChe from "./containers/PhieuXuatPhaChe";
import { Button, Dropdown, ModalSignPrint } from "components";
import { useLoading } from "hooks";
import { useParams } from "react-router-dom";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { useDispatch } from "react-redux";
import { SVG } from "assets";
import { openInNewTab } from "utils";
import printProvider from "data-access/print-provider";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { MA_BIEU_MAU_EDITOR } from "constants/index";

const PhieuChotPhaCheThuoc = () => {
  const { t } = useTranslation();
  const refModalNhapLyDo = useRef(null);
  const { id, nbDotDieuTriId } = useParams();
  const { showLoading, hideLoading } = useLoading();
  const refSelectRow = useRef(null);
  const refModalSignPrint = useRef(null);
  const refPhieuXuatPhaChe = useRef(null);

  const {
    nbChotPhaCheThuoc: { phaCheThuoc },
    phieuIn: { getListPhieu, showFileEditor, getFilePhieuIn },
  } = useDispatch();

  const [state, _setState] = useState({
    baoCao: "1",
    listPhieu: [],
  });
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  useEffect(() => {
    if (nbDotDieuTriId) {
      getListPhieu({
        nbDotDieuTriId: nbDotDieuTriId,
        maManHinh: "042",
        maViTri: "04201",
      }).then((listPhieu) => {
        setState({ listPhieu });
      });
    }
  }, [nbDotDieuTriId]);

  refSelectRow.current = (index) => {
    const indexNextItem = +state.baoCao + index;
    if (0 < indexNextItem && indexNextItem < 5) {
      setState({
        baoCao: "" + indexNextItem,
      });
    }
  };

  const handlePhaChe = async () => {
    try {
      showLoading();

      await phaCheThuoc([{ id: id }]);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieu = (item) => async () => {
    if (item.type == "editor") {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: nbDotDieuTriId,
          maManHinh: "042",
          maViTri: "04201",
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      showFileEditor({
        phieu: item,
        nbDotDieuTriId: nbDotDieuTriId,
        id: id,
        ma: item.ma,
        maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
          ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
          : "",
        mhParams,
      });
    } else {
      if (checkIsPhieuKySo(item)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              nbDotDieuTriId: nbDotDieuTriId,
              maManHinh: "042",
              maViTri: "04201",
              id: id,
            },
          });
      } else {
        try {
          showLoading();
          const { finalFile, dsPhieu } = await getFilePhieuIn({
            listPhieus: [item],
            id: id,
            showError: true,
          });
          if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
            openInNewTab(finalFile);
          } else {
            printProvider.printPdf(dsPhieu);
          }
        } catch (error) {
        } finally {
          hideLoading();
        }
      }
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  const renderActionPhaChe = () => {
    return (
      <>
        <Button
          type="primary"
          minWidth={100}
          iconHeight={15}
          onClick={handlePhaChe}
        >
          {t("phaCheThuoc.phaChe")}
        </Button>
        <Dropdown overlay={menu} trigger="click">
          <Button type="primary" rightIcon={<SVG.IcPrint />} iconHeight={15}>
            <span>{t("common.inGiayTo")}</span>
          </Button>
        </Dropdown>
      </>
    );
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("phaCheThuoc.phaCheThuoc"), link: "/pha-che-thuoc" },
        {
          title: t("phaCheThuoc.danhSachPhieuChotPhaChe"),
          link: "/pha-che-thuoc/danh-sach-phieu-chot-pha-che",
        },
        {
          title: t("phaCheThuoc.phieuChotPhaCheThuoc"),
          link: `/pha-che-thuoc/phieu-chot-pha-che-thuoc/chi-tiet/${id}`,
        },
      ]}
      style={{ pageBodyPadding: "0" }}
      actionRight={renderActionPhaChe()}
    >
      <div className="wrapper">
        <Row gutter={0} className="wrapper-inner">
          <Col span={24} className="main">
            <PhieuXuatPhaChe ref={refPhieuXuatPhaChe} />
          </Col>
        </Row>
      </div>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
      <ModalSignPrint ref={refModalSignPrint} />
    </MainPage>
  );
};

export default PhieuChotPhaCheThuoc;
