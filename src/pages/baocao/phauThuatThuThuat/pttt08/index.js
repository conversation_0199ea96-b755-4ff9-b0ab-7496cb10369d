import React from "react";
import moment from "moment";
import { Col, Row } from "antd";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { useLazyKVMap } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { PHAN_LOAI_DOI_TUONG_KCB } from "constants/index";
/**
 * PTTT08. Báo cáo PTTT theo yêu cầu
 *
 */

const PTTT08 = () => {
  const {
    baoCaoDaIn: { getPttt08 },
  } = useDispatch();

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("baoCao.chonNgay")}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("baoCao.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              hasAllOption
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsDoiTuongKcb: _state.dsDoiTuongKcb
      ? getPhanLoaiDoiTuongKcb(_state.dsDoiTuongKcb).referIds
      : null,
  });

  return (
    <BaseBaoCao
      title={t("baoCao.pttt08")}
      renderFilter={renderFilter}
      getBc={getPttt08}
      handleDataSearch={handleDataSearch}
      breadcrumb={[{ title: "PTTT08", link: "/bao-cao/pttt-08" }]}
    />
  );
};

export default PTTT08;
