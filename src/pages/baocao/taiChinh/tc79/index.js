import React, { useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, Row } from "antd";
import { useListAll, useQueryAll, useEnum } from "hooks";
import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { query } from "redux-store/stores";
import ChonThuNgan from "pages/baocao/BaseBaoCao/components/ChonThuNgan";
import { ENUM } from "constants";
import { LOAI_PHIEU_THU_TRONG_VIEN } from "pages/baocao/utils";
import { isNumber } from "utils/index";

const TC79 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc79 },
  } = useDispatch();

  const [listAllQuayTiepDon] = useListAll("quayTiepDon", { dsLoai: 20 }, true);
  const { data: listAllLoaiHinhThanhToan } = useQueryAll(
    query.loaiHinhThanhToan.queryAllLoaiHinhThanhToan
  );
  const { data: listAllCaLamViec } = useQueryAll(
    query.caLamViec.queryAllCaLamViec
  );
  const { data: listAllToaNha } = useQueryAll(query.toaNha.queryAllToaNha);
  const { data: listAllNhomDichVuCap1 } = useQueryAll(
    query.nhomDichVuCap1.queryAllNhomDichVuCap1
  );
  const [listLoaiPhieuThu] = useEnum(ENUM.LOAI_PHIEU_THU);

  const listEnumLoaiPhieuThuTrongVien = useMemo(() => {
    return listLoaiPhieuThu.map((x) => x.id).filter((x) => ![5, 6].includes(x));
  }, [listLoaiPhieuThu]);

  const listLoaiPhieuThuEnumCustom = useMemo(() => {
    return [
      { id: 6, ten: t("baoCao.thuNhaThuoc") },
      {
        id: LOAI_PHIEU_THU_TRONG_VIEN,
        ten: t("baoCao.thuTrongVien"),
      },
      { id: 5, ten: t("baoCao.thuNgoai") },
    ];
  }, [listLoaiPhieuThu]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <ChonThuNgan maBaoCao="TC79" onChange={onChange} _state={_state} />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiHinhThanhToan")}
            </label>
            <Select
              onChange={onChange("dsLoaiHinhThanhToanId", true)}
              value={_state.dsLoaiHinhThanhToanId}
              className="select input-filter"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
              mode={"multiple"}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("thuNgan.chonCaLamViec")}
              onChange={onChange("dsCaLamViecId", true)}
              value={_state.dsCaLamViecId}
              defaultValue={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsQuayId", true)}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsToaNhaId")}
              value={_state.dsToaNhaId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              onChange={onChange("dsNhomDichVuCap1Id")}
              value={_state.dsNhomDichVuCap1Id}
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              data={listAllNhomDichVuCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
            <Select
              onChange={onChange("dsLoaiPhieuThu")}
              value={_state.dsLoaiPhieuThu}
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              data={listLoaiPhieuThuEnumCustom}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.inChiTiet}
              onChange={onChange("inChiTiet")}
            >
              {t("baoCao.inChiTiet")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const convertDSLoaiPhieuThu = (data) => {
    if (!Array.isArray(data)) data = [data];

    return data
      .flatMap((item) => {
        if (item === LOAI_PHIEU_THU_TRONG_VIEN) {
          return listEnumLoaiPhieuThuTrongVien;
        }
        return item;
      })
      .filter((x) => isNumber(x));
  };

  const handleDataSearch = ({ _state }) => {
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsThuNganId: _state.dsThuNganId,
      dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsQuayId: _state.dsQuayId,
      dsToaNhaId: _state.dsToaNhaId,
      inChiTiet: _state.inChiTiet,
      dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
      dsLoaiPhieuThu: convertDSLoaiPhieuThu(_state.dsLoaiPhieuThu),
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc79")}
      breadcrumb={[{ title: "tc79", link: "/bao-cao/tc79" }]}
      renderFilter={renderFilter}
      getBc={getTc79}
      initState={{
        dsQuayId: [""],
        dsThuNganId: [""],
        dsLoaiHinhThanhToanId: [""],
        dsCaLamViecId: [""],
        dsToaNhaId: [""],
        inChiTiet: false,
        dsNhomDichVuCap1Id: [""],
        dsLoaiPhieuThu: [LOAI_PHIEU_THU_TRONG_VIEN],
      }}
      handleDataSearch={handleDataSearch}
    />
  );
};

export default TC79;
