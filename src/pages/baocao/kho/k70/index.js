import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { Col, message, Row } from "antd";
import moment from "moment";
import { useStore, useCache } from "hooks";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { isArray } from "utils/index";
import { CACHE_KEY } from "constants/index";

const LOAI_THOI_GIAN = [
  {
    id: 10,
    i18n: "khth.ngayTaoPhieu",
  },
  {
    id: 20,
    i18n: "thuNgan.ngayThanhToan",
  },
];

const MAU_BAO_CAO = [
  {
    id: 1,
    i18n: "baoCao.baoCaoThongKeNguoiBenhKhongLayDonThuoc",
  },
  {
    id: 2,
    i18n: "baoCao.baoCaoDanhSachBenhNhanChoLinhThuoc",
  },
];

const K70 = () => {
  const {
    baoCaoDaIn: { getK70 },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
  } = useDispatch();

  const listKhoUser = useStore("kho.listKhoUser", []);
  const [mauBaoCao, setMauBaoCao] = useCache(
    "",
    CACHE_KEY.DATA_MAU_BAO_CAO_K70,
    2,
    false
  );

  useEffect(() => {
    getKhoTheoTaiKhoan({
      page: "",
      size: "",
      active: true,
    });
  }, []);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoThoiGian")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              data={LOAI_THOI_GIAN}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonThoiGian")}
              onChange={onChange("tuThoiGian")}
              value={_state.tuThoiGian}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonTuThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denThoiGian")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonThoiGian")}
              onChange={onChange("denThoiGian")}
              value={_state.denThoiGian}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonDenThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              onChange={onChange("dsKhoId")}
              value={_state.dsKhoId}
              data={listKhoUser}
              mode={"multiple"}
            />
            {!_state.isValidData && !isArray(_state.dsKhoId) && (
              <div className="error">{t("baoCao.vuiLongChonKho")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.mauBaoCao")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              onChange={(e) => {
                setMauBaoCao(e, false);
                onChange("mauBaoCao")(e);
              }}
              value={_state.mauBaoCao}
              data={MAU_BAO_CAO}
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuThoiGian).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denThoiGian).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoId: _state.dsKhoId,
    loaiThoiGian: _state.loaiThoiGian,
    mauBaoCao: _state.mauBaoCao,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!isArray(_state.dsKhoId, 1)) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.k70")}
      breadcrumb={[{ title: "K70", link: "/bao-cao/k70" }]}
      initState={{ loaiThoiGian: 10, mauBaoCao: mauBaoCao }}
      renderFilter={renderFilter}
      handleDataSearch={handleDataSearch}
      getBc={getK70}
      beforeOk={beforeOk}
    />
  );
};

export default K70;
