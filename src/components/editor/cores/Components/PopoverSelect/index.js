import { Popover, Input } from "antd";
import React, {
  useState,
  useEffect,
  memo,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { get, isNumber } from "lodash";
import { DeboundInput } from "components/editor/config";
import { useRefFunc } from "hooks";
import { containText } from "utils";
import classNames from "classnames";
import { SVG } from "assets";
import { HOTKEY } from "constants";
import { GlobalStyle, PopoverStyled } from "./styled";

const PopoverSelect = forwardRef(
  (
    {
      value,
      onChangeValue,
      data,
      isMultiple,
      onChangeInput,
      refValue,
      labelByKey,
      isValueKey2,
      trigger,
      isShowSearch = false,
      disabled,
      splitStr = ", ",
      contentAlign,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const refAllData = useRef([]);
    const refIsChangeValue = useRef(false);
    const inputRef = useRef(null);
    const [state, _setState] = useState({
      value: [],
      searchText: "",
      open: false,
      curIndex: 0,
    });
    const setState = (data = {}) => {
      _setState((state) => ({ ...state, ...data }));
    };

    useImperativeHandle(ref, () => ({
      onOpenChange: (open) => {
        setState({
          open,
        });
      },
    }));

    useEffect(() => {
      if (state.open) {
        setState({
          curIndex: 0,
          searchText: "",
        });
      }
    }, [state.open]);

    const {
      phimTat: {
        onRegisterHotkey,
        onUnRegisterHotkey,
        onAddLayer,
        onRemoveLayer,
      },
    } = useDispatch();

    useEffect(() => {
      setState({
        value: value ? value : isMultiple ? [] : null,
      });
    }, [value]);

    const valueDisplay = useMemo(() => {
      if (isValueKey2 && !refIsChangeValue.current) {
        return <div dangerouslySetInnerHTML={{ __html: state.value }}></div>;
      } else {
        const itemSelecteds = (data || []).filter((el) =>
          isMultiple
            ? (state.value || []).includes(el.value)
            : state.value == el.value
        );
        const itemExitsKeyKhac = itemSelecteds.find((el) => el.valueKeyKhac);
        setState({
          itemSelected: itemExitsKeyKhac || {},
        });
        const label = itemSelecteds
          .map((item) => (labelByKey ? item[labelByKey] : item.label))
          .join(splitStr || ", ");

        return label;
      }
    }, [data, state.value, labelByKey, isValueKey2]);

    const onClick = (el) => () => {
      let data = {
        value: state.value,
      };
      inputRef.current?.focus();
      refIsChangeValue.current = true;
      if (isMultiple) {
        if (state.value.includes(el.value)) {
          data.value = state.value.filter((item) => item != el.value);
        } else {
          data.value = [...data.value, el.value].filter((el) => isNumber(el));
        }
      } else {
        if (state.value == el.value) {
          data.value = null;
        } else {
          data.value = el.value;
          data.itemSelected = el;
        }
      }

      //nếu dạng chọn 1 thì cho đóng luôn popover sau khi chọn
      setState({ ...data, ...(!isMultiple ? { open: false } : {}) });
      onChangeValue(data.value);
    };

    const handleChangeSearchText = (e) => {
      setState({
        searchText: e.target.value,
        curIndex: 0,
      });
    };

    const handleOpenChange = (newOpen) => {
      setState({
        open: newOpen,
      });
    };

    useEffect(() => {
      const layerId = "layer-popover-select";
      const hotKeys = [
        {
          keyCode: HOTKEY.UP,
          onEvent: () => {
            onSelectRow(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: () => {
            onSelectRow(1);
          },
        },
        {
          keyCode: HOTKEY.ENTER,
          onEvent: (e) => {
            onEnter();
          },
        },
      ];
      if (state.open) {
        onAddLayer({ layerId });
        onRegisterHotkey({
          layerId: layerId,
          hotKeys,
        });
      } else {
        onRemoveLayer({ layerId });
        onUnRegisterHotkey({
          layerId: layerId,
          hotKeys,
        });
      }
    }, [state.open]);

    useEffect(() => {
      if (state.open) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      } else {
        inputRef.current?.blur();
      }
    }, [state.open]);

    const filterData = useMemo(() => {
      if (state.searchText) {
        return data.filter((item) => containText(item.label, state.searchText));
      }
      return data;
    }, [data, state.searchText]);

    const onEnter = useRefFunc(() => {
      onClick(filterData[state.curIndex])();
    });

    const onSelectRow = useRefFunc((index) => {
      let indexNextItem = state.curIndex + index;
      if (indexNextItem === filterData.length) {
        indexNextItem = 0;
      } else if (indexNextItem < 0) {
        indexNextItem = filterData.length - 1;
      }

      setState({
        curIndex: indexNextItem,
      });
    });

    return (
      <div>
        {disabled ? (
          <div
            style={{
              width: "100%",
              minHeight: "14px",
              textAlign: "center",
              wordBreak: "break-word",
            }}
          >
            {valueDisplay || ""}
          </div>
        ) : (
          <Popover
            overlayClassName={"select-custorm-popover"}
            trigger={trigger || "hover"}
            title=""
            open={state.open}
            onOpenChange={handleOpenChange}
            placement="bottom"
            content={
              <PopoverStyled>
                {isShowSearch && (
                  <Input
                    ref={inputRef}
                    placeholder={t("Tìm kiếm")}
                    value={state.searchText}
                    onChange={handleChangeSearchText}
                    style={{}}
                  />
                )}

                <div className="content">
                  {(filterData || []).map((el, index) => {
                    const isActive = isMultiple
                      ? (state.value || []).includes(el.value)
                      : state.value == el.value;

                    const isFocusing = state.curIndex === index;

                    return (
                      <div
                        className={classNames("item", {
                          active: isActive,
                          focusing: isFocusing,
                        })}
                        onClick={onClick(el)}
                        key={index}
                        style={{
                          display: "flex",
                          alignContent: "center",
                          padding: "0 10px",
                        }}
                        onMouseEnter={() => {
                          setState({
                            curIndex: index,
                          });
                        }}
                      >
                        <span style={{ flex: 1, textAlign: "left" }}>
                          {el?.label}
                        </span>
                        {isActive && (
                          <SVG.IcDelete
                            className="ic-remove"
                            onClick={onClick(el)}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </PopoverStyled>
            }
          >
            <GlobalStyle></GlobalStyle>
            <div
              style={{
                width: "100%",
                minHeight: "14px",
                textAlign: contentAlign || "center",
                wordBreak: "break-word",
              }}
            >
              {valueDisplay || "...."}
            </div>
          </Popover>
        )}

        {state.itemSelected?.valueKeyKhac ? (
          <DeboundInput
            readOnly={false}
            value={get(refValue, state.itemSelected?.keyKhac, "")}
            onChange={(e) => {
              onChangeInput(state.itemSelected?.keyKhac)(e);
            }}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            contentAlign="center"
          />
        ) : null}
      </div>
    );
  }
);
export default memo(PopoverSelect);
