import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import React from "react";

// Page home
const SubPageBaoCao = React.lazy(() => import("pages/home/<USER>/BaoCao"));
const PK01 = React.lazy(() => import("pages/baocao/phongKham/pk01"));
const BC01 = React.lazy(() => import("pages/baocao/dichVu/bc01"));
const BC02 = React.lazy(() => import("pages/baocao/dichVu/bc02"));
const BC02_1 = React.lazy(() => import("pages/baocao/dichVu/bc02_1"));
const BC03 = React.lazy(() => import("pages/baocao/dichVu/bc03"));
const BC04 = React.lazy(() => import("pages/baocao/dichVu/bc04"));
const BC05 = React.lazy(() => import("pages/baocao/dichVu/bc05"));
const BC06 = React.lazy(() => import("pages/baocao/dichVu/bc06"));
const BC07 = React.lazy(() => import("pages/baocao/dichVu/bc07"));
const BC08 = React.lazy(() => import("pages/baocao/dichVu/bc08"));
const BC09 = React.lazy(() => import("pages/baocao/dichVu/bc09"));
const BC10 = React.lazy(() => import("pages/baocao/dichVu/bc10"));
const BC10_1 = React.lazy(() => import("pages/baocao/dichVu/bc10.1"));
const BC10_2 = React.lazy(() => import("pages/baocao/dichVu/bc10.2"));
const BC10_3 = React.lazy(() => import("pages/baocao/dichVu/bc10.3"));
const BC11 = React.lazy(() => import("pages/baocao/dichVu/bc11"));
const BC11_1 = React.lazy(() => import("pages/baocao/dichVu/bc11_1"));
const BC11_2 = React.lazy(() => import("pages/baocao/dichVu/bc11_2"));
const BC12 = React.lazy(() => import("pages/baocao/dichVu/bc12"));
const BC14 = React.lazy(() => import("pages/baocao/dichVu/bc14"));
const BC14_1 = React.lazy(() => import("pages/baocao/dichVu/bc14_1"));
const BC14_2 = React.lazy(() => import("pages/baocao/dichVu/bc14_2"));
const BC15 = React.lazy(() => import("pages/baocao/dichVu/bc15"));
const BC16 = React.lazy(() => import("pages/baocao/dichVu/bc16"));
const BC17 = React.lazy(() => import("pages/baocao/dichVu/bc17"));
const BC18 = React.lazy(() => import("pages/baocao/dichVu/bc18"));
const BC19 = React.lazy(() => import("pages/baocao/dichVu/bc19"));
const BC20 = React.lazy(() => import("pages/baocao/dichVu/bc20"));
const BC21 = React.lazy(() => import("pages/baocao/dichVu/bc21"));
const BC22 = React.lazy(() => import("pages/baocao/dichVu/bc22"));
const BC23 = React.lazy(() => import("pages/baocao/dichVu/bc23"));
const BC24 = React.lazy(() => import("pages/baocao/dichVu/bc24"));
const BC25 = React.lazy(() => import("pages/baocao/dichVu/bc25"));
const BC26 = React.lazy(() => import("pages/baocao/dichVu/bc26"));
const BC27 = React.lazy(() => import("pages/baocao/dichVu/bc27"));
const BC28 = React.lazy(() => import("pages/baocao/dichVu/bc28"));
const BC29 = React.lazy(() => import("pages/baocao/dichVu/bc29"));
const BC29_2 = React.lazy(() => import("pages/baocao/dichVu/bc29_2"));
const BC30 = React.lazy(() => import("pages/baocao/dichVu/bc30"));
const BC31 = React.lazy(() => import("pages/baocao/dichVu/bc31"));
const BC32 = React.lazy(() => import("pages/baocao/dichVu/bc32"));
const BC33 = React.lazy(() => import("pages/baocao/dichVu/bc33"));
const BC34 = React.lazy(() => import("pages/baocao/dichVu/bc34"));
const BC35 = React.lazy(() => import("pages/baocao/dichVu/bc35"));
const BC36 = React.lazy(() => import("pages/baocao/dichVu/bc36"));
const BC37 = React.lazy(() => import("pages/baocao/dichVu/bc37"));
const BC38 = React.lazy(() => import("pages/baocao/dichVu/bc38"));
const BC39 = React.lazy(() => import("pages/baocao/dichVu/bc39"));
const BC40 = React.lazy(() => import("pages/baocao/dichVu/bc40"));
const BC41 = React.lazy(() => import("pages/baocao/dichVu/bc41"));
const BC42 = React.lazy(() => import("pages/baocao/dichVu/bc42"));

const PC01 = React.lazy(() => import("pages/baocao/phaChe/pc01"));
const PC02 = React.lazy(() => import("pages/baocao/phaChe/pc02"));
const PC04 = React.lazy(() => import("pages/baocao/phaChe/pc04"));

const TongHopThuTienNb = React.lazy(() => import("pages/baocao/taiChinh/tc01"));
const TC01_1 = React.lazy(() => import("pages/baocao/taiChinh/tc01_1"));
const TC01_2 = React.lazy(() => import("pages/baocao/taiChinh/tc01_2"));
const TC01_3 = React.lazy(() => import("pages/baocao/taiChinh/tc01_3"));
const TC01_4 = React.lazy(() => import("pages/baocao/taiChinh/tc01_4"));
const TC01_5 = React.lazy(() => import("pages/baocao/taiChinh/tc01_5"));
const TC01_6 = React.lazy(() => import("pages/baocao/taiChinh/tc01_6"));
const TC01_7 = React.lazy(() => import("pages/baocao/taiChinh/tc01_7"));
const TC01_8 = React.lazy(() => import("pages/baocao/taiChinh/tc01_8"));
const TC01_9 = React.lazy(() => import("pages/baocao/taiChinh/tc01_9"));
const TC01_10 = React.lazy(() => import("pages/baocao/taiChinh/tc01_10"));
const TC01_11 = React.lazy(() => import("pages/baocao/taiChinh/tc01_11"));
const TC01_12 = React.lazy(() => import("pages/baocao/taiChinh/tc01_12"));
const TC01_13 = React.lazy(() => import("pages/baocao/taiChinh/tc01_13"));
const TC01_14 = React.lazy(() => import("pages/baocao/taiChinh/tc01_14"));
const TC01_15 = React.lazy(() => import("pages/baocao/taiChinh/tc01_15"));
const TC01_16 = React.lazy(() => import("pages/baocao/taiChinh/tc01_16"));
const TC02 = React.lazy(() => import("pages/baocao/taiChinh/tc02"));
const TC02_1 = React.lazy(() => import("pages/baocao/taiChinh/tc02_1"));
const TC02_2 = React.lazy(() => import("pages/baocao/taiChinh/tc02_2"));
const TC03 = React.lazy(() => import("pages/baocao/taiChinh/tc03"));
const TC03_2 = React.lazy(() => import("pages/baocao/taiChinh/tc03_2"));
const TC04 = React.lazy(() => import("pages/baocao/taiChinh/tc04"));
const TC05 = React.lazy(() => import("pages/baocao/taiChinh/tc05"));
const TC06 = React.lazy(() => import("pages/baocao/taiChinh/tc06"));
const TC06_1 = React.lazy(() => import("pages/baocao/taiChinh/tc06_1"));
const TC06_4 = React.lazy(() => import("pages/baocao/taiChinh/tc06_4"));
const TC06_5 = React.lazy(() => import("pages/baocao/taiChinh/tc06_5"));
const TC07 = React.lazy(() => import("pages/baocao/taiChinh/tc07"));
const TC08 = React.lazy(() => import("pages/baocao/taiChinh/tc08"));
const TC09 = React.lazy(() => import("pages/baocao/taiChinh/tc09"));
const TC10 = React.lazy(() => import("pages/baocao/taiChinh/tc10"));
const TC11 = React.lazy(() => import("pages/baocao/taiChinh/tc11"));
const TC12 = React.lazy(() => import("pages/baocao/taiChinh/tc12"));
const TC12_1 = React.lazy(() => import("pages/baocao/taiChinh/tc12_1"));
const TC13 = React.lazy(() => import("pages/baocao/taiChinh/tc13"));
const TC13_1 = React.lazy(() => import("pages/baocao/taiChinh/tc13_1"));
const TC13_2 = React.lazy(() => import("pages/baocao/taiChinh/tc13_2"));
const TC13_3 = React.lazy(() => import("pages/baocao/taiChinh/tc13_3"));
const TC14 = React.lazy(() => import("pages/baocao/taiChinh/tc14"));
const TC14_1 = React.lazy(() => import("pages/baocao/taiChinh/tc14_1"));
const TC15 = React.lazy(() => import("pages/baocao/taiChinh/tc15"));
const TC15_1 = React.lazy(() => import("pages/baocao/taiChinh/tc15_1"));
const TC15_2 = React.lazy(() => import("pages/baocao/taiChinh/tc15_2"));
const TC15_3 = React.lazy(() => import("pages/baocao/taiChinh/tc15_3"));
const TC15_4 = React.lazy(() => import("pages/baocao/taiChinh/tc15_4"));
const TC15_5 = React.lazy(() => import("pages/baocao/taiChinh/tc15_5"));
const TC16 = React.lazy(() => import("pages/baocao/taiChinh/tc16"));
const TC16_1 = React.lazy(() => import("pages/baocao/taiChinh/tc16_1"));
const TC17 = React.lazy(() => import("pages/baocao/taiChinh/tc17"));
const TC17_1 = React.lazy(() => import("pages/baocao/taiChinh/tc17_1"));
const TC17_2 = React.lazy(() => import("pages/baocao/taiChinh/tc17_2"));
const TC17_3 = React.lazy(() => import("pages/baocao/taiChinh/tc17_3"));
const TC18 = React.lazy(() => import("pages/baocao/taiChinh/tc18"));
const TC18_1 = React.lazy(() => import("pages/baocao/taiChinh/tc18_1"));
const TC18_2 = React.lazy(() => import("pages/baocao/taiChinh/tc18_2"));
const TC19 = React.lazy(() => import("pages/baocao/taiChinh/tc19"));
const TC20 = React.lazy(() => import("pages/baocao/taiChinh/tc20"));
const TC20_1 = React.lazy(() => import("pages/baocao/taiChinh/tc20_1"));
const TC21 = React.lazy(() => import("pages/baocao/taiChinh/tc21"));
const TC21_1 = React.lazy(() => import("pages/baocao/taiChinh/tc21_1"));
const TC21_2 = React.lazy(() => import("pages/baocao/taiChinh/tc21_2"));
const TC21_3 = React.lazy(() => import("pages/baocao/taiChinh/tc21_3"));
const TC21_4 = React.lazy(() => import("pages/baocao/taiChinh/tc21_4"));
const TC22 = React.lazy(() => import("pages/baocao/taiChinh/tc22"));
const TC22_1 = React.lazy(() => import("pages/baocao/taiChinh/tc22_1"));
const TC22_2 = React.lazy(() => import("pages/baocao/taiChinh/tc22_2"));
const TC22_3 = React.lazy(() => import("pages/baocao/taiChinh/tc22_3"));
const TC23 = React.lazy(() => import("pages/baocao/taiChinh/tc23"));
const TC24 = React.lazy(() => import("pages/baocao/taiChinh/tc24"));
const TC25 = React.lazy(() => import("pages/baocao/taiChinh/tc25"));
const TC26 = React.lazy(() => import("pages/baocao/taiChinh/tc26"));
const TC27 = React.lazy(() => import("pages/baocao/taiChinh/tc27"));
const TC28 = React.lazy(() => import("pages/baocao/taiChinh/tc28"));
const TC28_1 = React.lazy(() => import("pages/baocao/taiChinh/tc28_1"));
const TC29 = React.lazy(() => import("pages/baocao/taiChinh/tc29"));
const TC29_1 = React.lazy(() => import("pages/baocao/taiChinh/tc29_1"));
const TC29_2 = React.lazy(() => import("pages/baocao/taiChinh/tc29_2"));
const TC31 = React.lazy(() => import("pages/baocao/taiChinh/tc31"));
const TC33 = React.lazy(() => import("pages/baocao/taiChinh/tc33"));
const TC34 = React.lazy(() => import("pages/baocao/taiChinh/tc34"));
const TC36 = React.lazy(() => import("pages/baocao/taiChinh/tc36"));
const TC37 = React.lazy(() => import("pages/baocao/taiChinh/tc37"));
const TC38 = React.lazy(() => import("pages/baocao/taiChinh/tc38"));
const TC39 = React.lazy(() => import("pages/baocao/taiChinh/tc39"));
const TC40 = React.lazy(() => import("pages/baocao/taiChinh/tc40"));
const TC41 = React.lazy(() => import("pages/baocao/taiChinh/tc41"));
const TC41_1 = React.lazy(() => import("pages/baocao/taiChinh/tc41_1"));
const TC42 = React.lazy(() => import("pages/baocao/taiChinh/tc42"));
const TC42_1 = React.lazy(() => import("pages/baocao/taiChinh/tc42_1"));
const TC42_2 = React.lazy(() => import("pages/baocao/taiChinh/tc42_2"));
const TC42_3 = React.lazy(() => import("pages/baocao/taiChinh/tc42_3"));
const TC42_4 = React.lazy(() => import("pages/baocao/taiChinh/tc42_4"));
const TC42_5 = React.lazy(() => import("pages/baocao/taiChinh/tc42_5"));
const TC42_6 = React.lazy(() => import("pages/baocao/taiChinh/tc42_6"));
const TC46 = React.lazy(() => import("pages/baocao/taiChinh/tc46"));
const TC48 = React.lazy(() => import("pages/baocao/taiChinh/tc48"));
const TC49 = React.lazy(() => import("pages/baocao/taiChinh/tc49"));
const TC50 = React.lazy(() => import("pages/baocao/taiChinh/tc50"));
const TC51 = React.lazy(() => import("pages/baocao/taiChinh/tc51"));
const TC51_1 = React.lazy(() => import("pages/baocao/taiChinh/tc51_1"));
const TC53 = React.lazy(() => import("pages/baocao/taiChinh/tc53"));
const TC54 = React.lazy(() => import("pages/baocao/taiChinh/tc54"));
const TC54_1 = React.lazy(() => import("pages/baocao/taiChinh/tc54_1"));
const TC55 = React.lazy(() => import("pages/baocao/taiChinh/tc55"));
const TC55_1 = React.lazy(() => import("pages/baocao/taiChinh/tc55_1"));
const TC56 = React.lazy(() => import("pages/baocao/taiChinh/tc56"));
const TC57 = React.lazy(() => import("pages/baocao/taiChinh/tc57"));
const TC57_1 = React.lazy(() => import("pages/baocao/taiChinh/tc57_1"));
const TC58 = React.lazy(() => import("pages/baocao/taiChinh/tc58"));
const TC58_1 = React.lazy(() => import("pages/baocao/taiChinh/tc58_1"));
const TC59 = React.lazy(() => import("pages/baocao/taiChinh/tc59"));
const TC59_1 = React.lazy(() => import("pages/baocao/taiChinh/tc59_1"));
const TC59_2 = React.lazy(() => import("pages/baocao/taiChinh/tc59_2"));
const TC59_3 = React.lazy(() => import("pages/baocao/taiChinh/tc59_3"));
const TC60 = React.lazy(() => import("pages/baocao/taiChinh/tc60"));
const TC61 = React.lazy(() => import("pages/baocao/taiChinh/tc61"));
const TC62 = React.lazy(() => import("pages/baocao/taiChinh/tc62"));
const TC63 = React.lazy(() => import("pages/baocao/taiChinh/tc63"));
const TC63_1 = React.lazy(() => import("pages/baocao/taiChinh/tc63_1"));
const TC64 = React.lazy(() => import("pages/baocao/taiChinh/tc64"));
const TC64_1 = React.lazy(() => import("pages/baocao/taiChinh/tc64_1"));
const TC64_2 = React.lazy(() => import("pages/baocao/taiChinh/tc64_2"));
const TC66 = React.lazy(() => import("pages/baocao/taiChinh/tc66"));
const TC67 = React.lazy(() => import("pages/baocao/taiChinh/tc67"));
const TC67_1 = React.lazy(() => import("pages/baocao/taiChinh/tc67_1"));
const TC67_2 = React.lazy(() => import("pages/baocao/taiChinh/tc67_2"));
const TC68 = React.lazy(() => import("pages/baocao/taiChinh/tc68"));
const TC69 = React.lazy(() => import("pages/baocao/taiChinh/tc69"));
const TC69_1 = React.lazy(() => import("pages/baocao/taiChinh/tc69_1"));
const TC69_2 = React.lazy(() => import("pages/baocao/taiChinh/tc69_2"));
const TC70 = React.lazy(() => import("pages/baocao/taiChinh/tc70"));
const TC71 = React.lazy(() => import("pages/baocao/taiChinh/tc71"));
const TC72 = React.lazy(() => import("pages/baocao/taiChinh/tc72"));
const TC73 = React.lazy(() => import("pages/baocao/taiChinh/tc73"));
const TC73_1 = React.lazy(() => import("pages/baocao/taiChinh/tc73_1"));
const TC74 = React.lazy(() => import("pages/baocao/taiChinh/tc74"));
const TC75 = React.lazy(() => import("pages/baocao/taiChinh/tc75"));
const TC76 = React.lazy(() => import("pages/baocao/taiChinh/tc76"));
const TC77 = React.lazy(() => import("pages/baocao/taiChinh/tc77"));
const TC75_1 = React.lazy(() => import("pages/baocao/taiChinh/tc75_1"));
const TC79 = React.lazy(() => import("pages/baocao/taiChinh/tc79"));
const TC79_1 = React.lazy(() => import("pages/baocao/taiChinh/tc79_1"));
const TC80 = React.lazy(() => import("pages/baocao/taiChinh/tc80"));
const TC81 = React.lazy(() => import("pages/baocao/taiChinh/tc81"));
const TC80_1 = React.lazy(() => import("pages/baocao/taiChinh/tc80_1"));
const TC80_2 = React.lazy(() => import("pages/baocao/taiChinh/tc80_2"));
const TC81_1 = React.lazy(() => import("pages/baocao/taiChinh/tc81_1"));

const PK02 = React.lazy(() => import("pages/baocao/phongKham/pk02"));
const PK03 = React.lazy(() => import("pages/baocao/phongKham/pk03"));
const PK04 = React.lazy(() => import("pages/baocao/phongKham/pk04"));
const PK06 = React.lazy(() => import("pages/baocao/phongKham/pk06"));
const PK07 = React.lazy(() => import("pages/baocao/phongKham/pk07"));
const PK08 = React.lazy(() => import("pages/baocao/phongKham/pk08"));
const PK09 = React.lazy(() => import("pages/baocao/phongKham/pk09"));
const PK10 = React.lazy(() => import("pages/baocao/phongKham/pk10"));
const PK11 = React.lazy(() => import("pages/baocao/phongKham/pk11"));
const PK12 = React.lazy(() => import("pages/baocao/phongKham/pk12"));
const PK13 = React.lazy(() => import("pages/baocao/phongKham/pk13"));
const PK14 = React.lazy(() => import("pages/baocao/phongKham/pk14"));
const PK15 = React.lazy(() => import("pages/baocao/phongKham/pk15"));

const NhaCungCap = React.lazy(() => import("pages/baocao/kho/k01_1"));
const K01_2 = React.lazy(() => import("pages/baocao/kho/k01_2"));
const K01_3 = React.lazy(() => import("pages/baocao/kho/k01_3"));
const K01 = React.lazy(() => import("pages/baocao/kho/k01"));
const K02 = React.lazy(() => import("pages/baocao/kho/k02"));
const K02_1 = React.lazy(() => import("pages/baocao/kho/k02_1"));
const K02_2 = React.lazy(() => import("pages/baocao/kho/k02_2"));
const K02_3 = React.lazy(() => import("pages/baocao/kho/k02_3"));
const K02_4 = React.lazy(() => import("pages/baocao/kho/k02_4"));
const K02_5 = React.lazy(() => import("pages/baocao/kho/k02_5"));
const K02_6 = React.lazy(() => import("pages/baocao/kho/k02_6"));
const K02_7 = React.lazy(() => import("pages/baocao/kho/k02_7"));
const K02_8 = React.lazy(() => import("pages/baocao/kho/k02_8"));
const K02_10 = React.lazy(() => import("pages/baocao/kho/k02_10"));
const K03 = React.lazy(() => import("pages/baocao/kho/k03"));
const K04 = React.lazy(() => import("pages/baocao/kho/k04"));
const K04_1 = React.lazy(() => import("pages/baocao/kho/k04_1"));
const K04_2 = React.lazy(() => import("pages/baocao/kho/k04_2"));
const K04_3 = React.lazy(() => import("pages/baocao/kho/k04_3"));
const K04_4 = React.lazy(() => import("pages/baocao/kho/k04_4"));
const K05 = React.lazy(() => import("pages/baocao/kho/k05"));
const K05_1 = React.lazy(() => import("pages/baocao/kho/k05_1"));
const K07 = React.lazy(() => import("pages/baocao/kho/k07"));
const K07_1 = React.lazy(() => import("pages/baocao/kho/k07_1"));
const K08 = React.lazy(() => import("pages/baocao/kho/k08"));
const K10 = React.lazy(() => import("pages/baocao/kho/k10"));
const K11 = React.lazy(() => import("pages/baocao/kho/k11"));
const K12 = React.lazy(() => import("pages/baocao/kho/k12"));
const K13 = React.lazy(() => import("pages/baocao/kho/k13"));
const K14 = React.lazy(() => import("pages/baocao/kho/k14"));
const K14_1 = React.lazy(() => import("pages/baocao/kho/k14_1"));
const K14_2 = React.lazy(() => import("pages/baocao/kho/k14_2"));
const K15 = React.lazy(() => import("pages/baocao/kho/k15"));
const K20 = React.lazy(() => import("pages/baocao/kho/k20"));
const K20_1 = React.lazy(() => import("pages/baocao/kho/k20_1"));
const K20_2 = React.lazy(() => import("pages/baocao/kho/k20_2"));
const K20_3 = React.lazy(() => import("pages/baocao/kho/k20_3"));
const K21 = React.lazy(() => import("pages/baocao/kho/k21"));
const K22 = React.lazy(() => import("pages/baocao/kho/k22"));
const K23 = React.lazy(() => import("pages/baocao/kho/k23"));
const K24 = React.lazy(() => import("pages/baocao/kho/k24"));
const K25 = React.lazy(() => import("pages/baocao/kho/k25"));
const K26 = React.lazy(() => import("pages/baocao/kho/k26"));
const K27 = React.lazy(() => import("pages/baocao/kho/k27"));
const K28 = React.lazy(() => import("pages/baocao/kho/k28"));
const K29 = React.lazy(() => import("pages/baocao/kho/k29"));
const K30 = React.lazy(() => import("pages/baocao/kho/k30"));
const K31 = React.lazy(() => import("pages/baocao/kho/k31"));
const K32 = React.lazy(() => import("pages/baocao/kho/k32"));
const K33 = React.lazy(() => import("pages/baocao/kho/k33"));
const K34 = React.lazy(() => import("pages/baocao/kho/k34"));
const K35 = React.lazy(() => import("pages/baocao/kho/k35"));
const K36 = React.lazy(() => import("pages/baocao/kho/k36"));
const K37 = React.lazy(() => import("pages/baocao/kho/k37"));
const K38 = React.lazy(() => import("pages/baocao/kho/k38"));
const K39 = React.lazy(() => import("pages/baocao/kho/k39"));
const K40 = React.lazy(() => import("pages/baocao/kho/k40"));
const K42 = React.lazy(() => import("pages/baocao/kho/k42"));
const K43 = React.lazy(() => import("pages/baocao/kho/k43"));
const K44 = React.lazy(() => import("pages/baocao/kho/k44"));
const K45 = React.lazy(() => import("pages/baocao/kho/k45"));
const K46 = React.lazy(() => import("pages/baocao/kho/k46"));
const K47 = React.lazy(() => import("pages/baocao/kho/k47"));
const K48 = React.lazy(() => import("pages/baocao/kho/k48"));
const K49 = React.lazy(() => import("pages/baocao/kho/k49"));
const K50 = React.lazy(() => import("pages/baocao/kho/k50"));
const K51 = React.lazy(() => import("pages/baocao/kho/k51"));
const K52 = React.lazy(() => import("pages/baocao/kho/k52"));
const K53 = React.lazy(() => import("pages/baocao/kho/k53"));
const K54 = React.lazy(() => import("pages/baocao/kho/k54"));
const K55 = React.lazy(() => import("pages/baocao/kho/k55"));
const K56 = React.lazy(() => import("pages/baocao/kho/k56"));
const K57 = React.lazy(() => import("pages/baocao/kho/k57"));
const K58 = React.lazy(() => import("pages/baocao/kho/k58"));
const K58_1 = React.lazy(() => import("pages/baocao/kho/k58_1"));
const K59 = React.lazy(() => import("pages/baocao/kho/k59"));
const K60 = React.lazy(() => import("pages/baocao/kho/k60"));
const K61 = React.lazy(() => import("pages/baocao/kho/k61"));
const K62 = React.lazy(() => import("pages/baocao/kho/k62"));
const K63 = React.lazy(() => import("pages/baocao/kho/k63"));
const K64 = React.lazy(() => import("pages/baocao/kho/k64"));
const K65 = React.lazy(() => import("pages/baocao/kho/k65"));
const K66 = React.lazy(() => import("pages/baocao/kho/k66"));
const K67 = React.lazy(() => import("pages/baocao/kho/k67"));
const K68 = React.lazy(() => import("pages/baocao/kho/k68"));
const K69 = React.lazy(() => import("pages/baocao/kho/k69"));
const K70 = React.lazy(() => import("pages/baocao/kho/k70"));
const K71 = React.lazy(() => import("pages/baocao/kho/k71"));
const K72 = React.lazy(() => import("pages/baocao/kho/k72"));
const K73 = React.lazy(() => import("pages/baocao/kho/k73"));
const K74 = React.lazy(() => import("pages/baocao/kho/k74"));
const K75 = React.lazy(() => import("pages/baocao/kho/k75"));
const K76 = React.lazy(() => import("pages/baocao/kho/k76"));
const K77 = React.lazy(() => import("pages/baocao/kho/k77"));
const K78 = React.lazy(() => import("pages/baocao/kho/k78"));
const K79 = React.lazy(() => import("pages/baocao/kho/k79"));
const K80 = React.lazy(() => import("pages/baocao/kho/k80"));
const K82 = React.lazy(() => import("pages/baocao/kho/k82"));
const K84 = React.lazy(() => import("pages/baocao/kho/k84"));
const KNT05 = React.lazy(() => import("pages/baocao/nhaThuoc/knt05"));
const KNT03 = React.lazy(() => import("pages/baocao/nhaThuoc/knt03"));
const KNT02 = React.lazy(() => import("pages/baocao/nhaThuoc/knt02"));
const KNT04 = React.lazy(() => import("pages/baocao/nhaThuoc/knt04"));
const KNT03_1 = React.lazy(() => import("pages/baocao/nhaThuoc/knt03_1"));

const KVT01_1 = React.lazy(() => import("pages/baocao/khoVatTu/kvt01_1"));
const KVT02 = React.lazy(() => import("pages/baocao/khoVatTu/kvt02"));
const KVT03 = React.lazy(() => import("pages/baocao/khoVatTu/kvt03"));
const Kvt04 = React.lazy(() => import("pages/baocao/khoVatTu/kvt04"));
const KVT05 = React.lazy(() => import("pages/baocao/khoVatTu/kvt05"));
const KVT06 = React.lazy(() => import("pages/baocao/khoVatTu/kvt06"));
const KVT07 = React.lazy(() => import("pages/baocao/khoVatTu/kvt07"));
const KNT01 = React.lazy(() => import("pages/baocao/nhaThuoc/knt01"));
const KNT06 = React.lazy(() => import("pages/baocao/nhaThuoc/knt06"));
const KNT07 = React.lazy(() => import("pages/baocao/nhaThuoc/knt07"));
const KNT08 = React.lazy(() => import("pages/baocao/nhaThuoc/knt08"));
const KNT08_1 = React.lazy(() => import("pages/baocao/nhaThuoc/knt08_1"));
const KNT10 = React.lazy(() => import("pages/baocao/nhaThuoc/knt10"));
const KNT11 = React.lazy(() => import("pages/baocao/nhaThuoc/knt11"));
const KNT12 = React.lazy(() => import("pages/baocao/nhaThuoc/knt12"));
const KNT13 = React.lazy(() => import("pages/baocao/nhaThuoc/knt13"));
const KNT14 = React.lazy(() => import("pages/baocao/nhaThuoc/knt14"));
const KNT15 = React.lazy(() => import("pages/baocao/nhaThuoc/knt15"));
const KNT15_1 = React.lazy(() => import("pages/baocao/nhaThuoc/knt15_1"));
const KNT16 = React.lazy(() => import("pages/baocao/nhaThuoc/knt16"));
const KNT17 = React.lazy(() => import("pages/baocao/nhaThuoc/knt17"));
const KNT18 = React.lazy(() => import("pages/baocao/nhaThuoc/knt18"));
const KNT19 = React.lazy(() => import("pages/baocao/nhaThuoc/knt19"));
const KNT20 = React.lazy(() => import("pages/baocao/nhaThuoc/knt20"));
const KNT21 = React.lazy(() => import("pages/baocao/nhaThuoc/knt21"));
const KNT22 = React.lazy(() => import("pages/baocao/nhaThuoc/knt22"));
const KNT23 = React.lazy(() => import("pages/baocao/nhaThuoc/knt23"));
const KNT24 = React.lazy(() => import("pages/baocao/nhaThuoc/knt24"));
const KNT25 = React.lazy(() => import("pages/baocao/nhaThuoc/knt25"));
const KNT26 = React.lazy(() => import("pages/baocao/nhaThuoc/knt26"));

const KSK01 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk01"));
const KSK01_1 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk01.1"));
const KSK02 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk02"));
const KSK04 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk04"));
const KSK05 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk05"));
const KSK12 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk12"));
const KSK13 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk13"));
const KSK15 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk15"));
const KSK16 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk16"));
const KSK17 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk17"));
const KSK18 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk18"));
const KSK19 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk19"));
const KSK20 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk20"));
const KSK20_1 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk20_1"));
const KSK21 = React.lazy(() => import("pages/baocao/khamSucKhoe/ksk21"));
const G01 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g01"));
const G02 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g02"));
const G03 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g03"));
const G04 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g04"));
const G05 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g05"));
const G06 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g06"));
const G07 = React.lazy(() => import("pages/baocao/goiLieuTrinh/g07"));
const KHTH01 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth01"));
const KHTH02 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth02"));
const KHTH03 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth03"));
const KHTH04 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth04"));
const KHTH05 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth05"));
const KHTH06 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth06"));
const KHTH07 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth07"));
const KHTH08 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth08"));
const KHTH08_1 = React.lazy(() =>
  import("pages/baocao/keHoachTongHop/khth08_1")
);
const KHTH08_2 = React.lazy(() =>
  import("pages/baocao/keHoachTongHop/khth08_2")
);
const KHTH09 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth09"));
const KHTH10 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth10"));
const KHTH11 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth11"));
const KHTH12 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth12"));
const KHTH12_1 = React.lazy(() =>
  import("pages/baocao/keHoachTongHop/khth12_1")
);
const KHTH13 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth13"));
const KHTH14 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth14"));
const KHTH15 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth15"));
const KHTH16 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth16"));
const KHTH17 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth17"));
const KHTH18 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth18"));
const KHTH19 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth19"));
const KHTH20 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth20"));
const KHTH21 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth21"));
const KHTH22 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth22"));
const KHTH23 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth23"));
const KHTH24 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth24"));
const KHTH25 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth25"));
const KHTH26 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth26"));
const KHTH27 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth27"));
const KHTH29 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth29"));
const KHTH30 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth30"));
const KHTH31 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth31"));
const KHTH33 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth33"));
const KHTH34 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth34"));
const KHTH35 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth35"));
const KHTH37 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth37"));
const KHTH38 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth38"));
const KHTH40 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth40"));
const KHTH41 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth41"));
const KHTH42 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth42"));
const KHTH43 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth43"));
const KHTH44 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth44"));
const KHTH45 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth45"));
const KHTH46 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth46"));
const KHTH47 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth47"));
const KHTH48 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth48"));
const KHTH49 = React.lazy(() => import("pages/baocao/keHoachTongHop/khth49"));

const PTTT01 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt01")
);
const PTTT02 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt02")
);
const PTTT03 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt03")
);
const PTTT04 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04")
);
const PTTT04_1 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_1")
);
const PTTT04_2 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_2")
);
const PTTT04_3 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_3")
);
const PTTT04_4 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_4")
);
const PTTT04_5 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_5")
);
const PTTT04_7 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_7")
);
const PTTT04_8 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_8")
);
const PTTT04_9 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt04_9")
);
const PTTT05 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt05")
);
const PTTT06 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt06")
);
const PTTT07 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt07")
);
const PTTT08 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt08")
);
const PTTT09 = React.lazy(() =>
  import("pages/baocao/phauThuatThuThuat/pttt09")
);
const DD01 = React.lazy(() => import("pages/baocao/suatAn/dd01"));
const DD03 = React.lazy(() => import("pages/baocao/suatAn/dd03"));

const TIEM01 = React.lazy(() => import("pages/baocao/tiemChung/tiem01"));
const TIEM02 = React.lazy(() => import("pages/baocao/tiemChung/tiem02"));
const TIEM03 = React.lazy(() => import("pages/baocao/tiemChung/tiem03"));
const TIEM04 = React.lazy(() => import("pages/baocao/tiemChung/tiem04"));
const TIEM05 = React.lazy(() => import("pages/baocao/tiemChung/tiem05"));

const KSNK_01 = React.lazy(() => import("pages/baocao/ksnk/ksnk_01"));
const KSNK_02 = React.lazy(() => import("pages/baocao/ksnk/ksnk_02"));

const LAO01 = React.lazy(() => import("pages/baocao/lao/lao01"));

const KDD01 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd01"));
const KDD02 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd02"));
const KDD03 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd03"));
const KDD04 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd04"));
const KDD05 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd05"));
const KDD09 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd09"));
const KDD10 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd10"));
const KDD11 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd11"));
const KDD12 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd12"));
const KDD07 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd07"));
const KDD08 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd08"));
const KDD13 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd13"));
const KDD14 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd14"));
const KDD06 = React.lazy(() => import("pages/baocao/khoDinhDuong/kdd06"));

//Báo cáo quản trị
const QT01 = React.lazy(() => import("pages/baocao/quanTri/qt01"));
const QT02 = React.lazy(() => import("pages/baocao/quanTri/qt02"));
const QT03 = React.lazy(() => import("pages/baocao/quanTri/qt03"));

// Báo cáo quyết toán
const QTBH13 = React.lazy(() => import("pages/baocao/quyetToanBh/qtbh13"));

// Báo cáo duyệt dược lâm sàng
const DDLS05 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls05"));
const DDLS06 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls06"));
const DDLS07 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls07"));
const DDLS08 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls08"));
const DDLS09 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls09"));
const DDLS10 = React.lazy(() => import("pages/baocao/duyetDuocLamSang/ddls10"));

// Báo cáo sang lọc dinh dưỡng
const SLDD01 = React.lazy(() => import("pages/baocao/sangLocDinhDuong/sldd01"));
const SLDD02 = React.lazy(() => import("pages/baocao/sangLocDinhDuong/sldd02"));
const SLDD03 = React.lazy(() => import("pages/baocao/sangLocDinhDuong/sldd03"));
const SLDD05 = React.lazy(() => import("pages/baocao/sangLocDinhDuong/sldd05"));
const SLDD06 = React.lazy(() => import("pages/baocao/sangLocDinhDuong/sldd06"));

export default {
  subPageBaoCao: {
    component: Page(SubPageBaoCao, []),
    accessRoles: [],
    path: "/bao-cao",
    exact: true,
  },
  pk01: {
    component: Page(PK01, [ROLES["BAO_CAO"].NGUOI_BENH_KHAM_CHI_TIET]),
    accessRoles: [],
    path: "/bao-cao/danh-sach-nguoi-benh-kham-chi-tiet",
    exact: true,
  },
  pk02: {
    component: Page(PK02, [ROLES["BAO_CAO"].NGUOI_BENH_CO_LICH_HEN_KHAM]),
    accessRoles: [],
    path: "/bao-cao/danh-sach-nguoi-benh-co-lich-hen-kham",
    exact: true,
  },
  pk03: {
    component: Page(PK03, [ROLES["BAO_CAO"].PK03]),
    accessRoles: [],
    path: "/bao-cao/pk-03",
    exact: true,
  },
  pk04: {
    component: Page(PK04, [ROLES["BAO_CAO"].PK04]),
    accessRoles: [ROLES["BAO_CAO"].PK04],
    path: "/bao-cao/pk-04",
    exact: true,
  },
  pk04_1: {
    component: Page(PK04, [ROLES["BAO_CAO"].PK04]),
    accessRoles: [ROLES["BAO_CAO"].PK04],
    path: "/bao-cao/pk-04_1",
    exact: true,
  },
  pk06: {
    component: Page(PK06, [ROLES["BAO_CAO"].PK06]),
    accessRoles: [ROLES["BAO_CAO"].PK06],
    path: "/bao-cao/pk-06",
    exact: true,
  },
  pk07: {
    component: Page(PK07, [ROLES["BAO_CAO"].PK07]),
    accessRoles: [],
    path: "/bao-cao/pk-07",
    exact: true,
  },
  pk08: {
    component: Page(PK08, [ROLES["BAO_CAO"].PK08]),
    accessRoles: [ROLES["BAO_CAO"].PK08],
    path: "/bao-cao/pk-08",
    exact: true,
  },
  pk09: {
    component: Page(PK09, [ROLES["BAO_CAO"].PK09]),
    accessRoles: [ROLES["BAO_CAO"].PK09],
    path: "/bao-cao/pk-09",
    exact: true,
  },
  pk10: {
    component: Page(PK10, [ROLES["BAO_CAO"].PK10]),
    accessRoles: [ROLES["BAO_CAO"].PK10],
    path: "/bao-cao/pk-10",
    exact: true,
  },
  pk11: {
    component: Page(PK11, [ROLES["BAO_CAO"].PK11]),
    accessRoles: [ROLES["BAO_CAO"].PK11],
    path: "/bao-cao/pk-11",
    exact: true,
  },
  pk12: {
    component: Page(PK12, [ROLES["BAO_CAO"].PK12]),
    accessRoles: [ROLES["BAO_CAO"].PK12],
    path: "/bao-cao/pk-12",
    exact: true,
  },
  pk13: {
    component: Page(PK13, [ROLES["BAO_CAO"].PK13]),
    accessRoles: [ROLES["BAO_CAO"].PK13],
    path: "/bao-cao/pk-13",
    exact: true,
  },
  pk14: {
    component: Page(PK14, [ROLES["BAO_CAO"].PK14]),
    accessRoles: [ROLES["BAO_CAO"].PK14],
    path: "/bao-cao/pk-14",
    exact: true,
  },
  pk15: {
    component: Page(PK15, [ROLES["BAO_CAO"].PK15]),
    accessRoles: [ROLES["BAO_CAO"].PK15],
    path: "/bao-cao/pk-15",
    exact: true,
  },
  bc01: {
    component: Page(BC01, [ROLES["BAO_CAO"].BC01]),
    accessRoles: [],
    path: "/bao-cao/bc-01",
    exact: true,
  },
  bc02: {
    component: Page(BC02, [ROLES["BAO_CAO"].BC02]),
    accessRoles: [],
    path: "/bao-cao/chi-tiet-nguoi-benh-da-tiep-don",
    exact: true,
  },
  bc02_1: {
    component: Page(BC02_1, [ROLES["BAO_CAO"].BC02_1]),
    accessRoles: [ROLES["BAO_CAO"].BC02_1],
    path: "/bao-cao/bc-02_1",
    exact: true,
  },
  bc03: {
    component: Page(BC03, [ROLES["BAO_CAO"].BC03]),
    accessRoles: [],
    path: "/bao-cao/bc-03",
    exact: true,
  },
  bc04: {
    component: Page(BC04, [ROLES["BAO_CAO"].BC04]),
    accessRoles: [],
    path: "/bao-cao/bc-04",
    exact: true,
  },
  bc05: {
    component: Page(BC05, [ROLES["BAO_CAO"].BC05]),
    accessRoles: [],
    path: "/bao-cao/bc-05",
    exact: true,
  },
  bc06: {
    component: Page(BC06, [ROLES["BAO_CAO"].BC06]),
    accessRoles: [],
    path: "/bao-cao/bc-06",
    exact: true,
  },
  bc07: {
    component: Page(BC07, [ROLES["BAO_CAO"].BC07]),
    accessRoles: [],
    path: "/bao-cao/bc-07",
    exact: true,
  },
  bc08: {
    component: Page(BC08, [ROLES["BAO_CAO"].BC08]),
    accessRoles: [],
    path: "/bao-cao/bc-08",
    exact: true,
  },
  bc09: {
    component: Page(BC09, [ROLES["BAO_CAO"].BC09]),
    accessRoles: [],
    path: "/bao-cao/bc-09",
    exact: true,
  },
  bc10: {
    component: Page(BC10, [ROLES["BAO_CAO"].BC10]),
    accessRoles: [],
    path: "/bao-cao/bc-10",
    exact: true,
  },
  bc10_1: {
    component: Page(BC10_1, [ROLES["BAO_CAO"].BC10_1]),
    accessRoles: [],
    path: "/bao-cao/bc-10_1",
    exact: true,
  },
  bc10_2: {
    component: Page(BC10_2, [ROLES["BAO_CAO"].BC10_2]),
    accessRoles: [],
    path: "/bao-cao/bc-10_2",
    exact: true,
  },
  bc10_3: {
    component: Page(BC10_3, [ROLES["BAO_CAO"].BC10_3]),
    accessRoles: [],
    path: "/bao-cao/bc-10_3",
    exact: true,
  },
  bc11: {
    component: Page(BC11, [ROLES["BAO_CAO"].BC11]),
    accessRoles: [],
    path: "/bao-cao/bc-11",
    exact: true,
  },
  bc11_1: {
    component: Page(BC11_1, [ROLES["BAO_CAO"].BC11_1]),
    accessRoles: [ROLES["BAO_CAO"].BC11_1],
    path: "/bao-cao/bc-11_1",
    exact: true,
  },
  bc11_2: {
    component: Page(BC11_2, [ROLES["BAO_CAO"].BC11_2]),
    accessRoles: [ROLES["BAO_CAO"].BC11_2],
    path: "/bao-cao/bc-11_2",
    exact: true,
  },
  bc12: {
    component: Page(BC12, [ROLES["BAO_CAO"].BC12]),
    accessRoles: [ROLES["BAO_CAO"].BC12],
    path: "/bao-cao/bc-12",
    exact: true,
  },
  bc14: {
    component: Page(BC14, [ROLES["BAO_CAO"].BC14]),
    accessRoles: [ROLES["BAO_CAO"].BC14],
    path: "/bao-cao/bc-14",
    exact: true,
  },
  bc14_1: {
    component: Page(BC14_1, [ROLES["BAO_CAO"].BC14_1]),
    accessRoles: [ROLES["BAO_CAO"].BC14_1],
    path: "/bao-cao/bc-14_1",
    exact: true,
  },
  bc14_2: {
    component: Page(BC14_2, [ROLES["BAO_CAO"].BC14_2]),
    accessRoles: [ROLES["BAO_CAO"].BC14_2],
    path: "/bao-cao/bc-14_2",
    exact: true,
  },
  bc15: {
    component: Page(BC15, [ROLES["BAO_CAO"].BC15]),
    accessRoles: [ROLES["BAO_CAO"].BC15],
    path: "/bao-cao/bc-15",
    exact: true,
  },
  bc16: {
    component: Page(BC16, [ROLES["BAO_CAO"].BC16]),
    accessRoles: [ROLES["BAO_CAO"].BC16],
    path: "/bao-cao/bc-16",
    exact: true,
  },
  bc17: {
    component: Page(BC17, [ROLES["BAO_CAO"].BC17]),
    accessRoles: [],
    path: "/bao-cao/bc-17",
    exact: true,
  },
  bc18: {
    component: Page(BC18, [ROLES["BAO_CAO"].BC18]),
    accessRoles: [ROLES["BAO_CAO"].BC18],
    path: "/bao-cao/bc-18",
    exact: true,
  },
  bc19: {
    component: Page(BC19, [ROLES["BAO_CAO"].BC19]),
    accessRoles: [ROLES["BAO_CAO"].BC19],
    path: "/bao-cao/bc-19",
    exact: true,
  },
  bc20: {
    component: Page(BC20, [ROLES["BAO_CAO"].BC20]),
    accessRoles: [ROLES["BAO_CAO"].BC20],
    path: "/bao-cao/bc-20",
    exact: true,
  },
  bc21: {
    component: Page(BC21, [ROLES["BAO_CAO"].BC21]),
    accessRoles: [ROLES["BAO_CAO"].BC21],
    path: "/bao-cao/bc-21",
    exact: true,
  },
  bc22: {
    component: Page(BC22, [ROLES["BAO_CAO"].BC22]),
    accessRoles: [ROLES["BAO_CAO"].BC22],
    path: "/bao-cao/bc-22",
    exact: true,
  },
  bc23: {
    component: Page(BC23, [ROLES["BAO_CAO"].BC23]),
    accessRoles: [ROLES["BAO_CAO"].BC23],
    path: "/bao-cao/bc-23",
    exact: true,
  },
  bc24: {
    component: Page(BC24, [ROLES["BAO_CAO"].BC24]),
    accessRoles: [ROLES["BAO_CAO"].BC24],
    path: "/bao-cao/bc-24",
    exact: true,
  },
  bc25: {
    component: Page(BC25, [ROLES["BAO_CAO"].BC25]),
    accessRoles: [ROLES["BAO_CAO"].BC25],
    path: "/bao-cao/bc-25",
    exact: true,
  },
  bc26: {
    component: Page(BC26, [ROLES["BAO_CAO"].BC26]),
    accessRoles: [ROLES["BAO_CAO"].BC26],
    path: "/bao-cao/bc-26",
    exact: true,
  },
  bc27: {
    component: Page(BC27, [ROLES["BAO_CAO"].BC27]),
    accessRoles: [ROLES["BAO_CAO"].BC27],
    path: "/bao-cao/bc-27",
    exact: true,
  },
  bc28: {
    component: Page(BC28, [ROLES["BAO_CAO"].BC28]),
    accessRoles: [ROLES["BAO_CAO"].BC28],
    path: "/bao-cao/bc-28",
    exact: true,
  },
  bc29: {
    component: Page(BC29, [ROLES["BAO_CAO"].BC29]),
    accessRoles: [ROLES["BAO_CAO"].BC29],
    path: "/bao-cao/bc-29",
    exact: true,
  },
  bc29_2: {
    component: Page(BC29_2, [ROLES["BAO_CAO"].BC29_2]),
    accessRoles: [ROLES["BAO_CAO"].BC29_2],
    path: "/bao-cao/bc-29_2",
    exact: true,
  },
  bc30: {
    component: Page(BC30, [ROLES["BAO_CAO"].BC30]),
    accessRoles: [ROLES["BAO_CAO"].BC30],
    path: "/bao-cao/bc-30",
    exact: true,
  },
  bc31: {
    component: Page(BC31, [ROLES["BAO_CAO"].BC31]),
    accessRoles: [ROLES["BAO_CAO"].BC31],
    path: "/bao-cao/bc-31",
    exact: true,
  },
  bc32: {
    component: Page(BC32, [ROLES["BAO_CAO"].BC32]),
    accessRoles: [ROLES["BAO_CAO"].BC32],
    path: "/bao-cao/bc-32",
    exact: true,
  },
  bc33: {
    component: Page(BC33, [ROLES["BAO_CAO"].BC33]),
    accessRoles: [ROLES["BAO_CAO"].BC33],
    path: "/bao-cao/bc-33",
    exact: true,
  },
  bc34: {
    component: Page(BC34, [ROLES["BAO_CAO"].BC34]),
    accessRoles: [],
    path: "/bao-cao/bc-34",
    exact: true,
  },
  bc35: {
    component: Page(BC35, [ROLES["BAO_CAO"].BC35]),
    accessRoles: [],
    path: "/bao-cao/bc-35",
    exact: true,
  },
  bc36: {
    component: Page(BC36, [ROLES["BAO_CAO"].BC36]),
    accessRoles: [],
    path: "/bao-cao/bc-36",
    exact: true,
  },
  bc37: {
    component: Page(BC37, [ROLES["BAO_CAO"].BC37]),
    accessRoles: [],
    path: "/bao-cao/bc-37",
    exact: true,
  },
  bc38: {
    component: Page(BC38, [ROLES["BAO_CAO"].BC38]),
    accessRoles: [ROLES["BAO_CAO"].BC38],
    path: "/bao-cao/bc-38",
    exact: true,
  },
  bc39: {
    component: Page(BC39, [ROLES["BAO_CAO"].BC39]),
    accessRoles: [ROLES["BAO_CAO"].BC39],
    path: "/bao-cao/bc-39",
    exact: true,
  },
  bc40: {
    component: Page(BC40, [ROLES["BAO_CAO"].BC40]),
    accessRoles: [ROLES["BAO_CAO"].BC40],
    path: "/bao-cao/bc-40",
    exact: true,
  },
  bc41: {
    component: Page(BC41, [ROLES["BAO_CAO"].BC41]),
    accessRoles: [ROLES["BAO_CAO"].BC41],
    path: "/bao-cao/bc-41",
    exact: true,
  },
  bc42: {
    component: Page(BC42, [ROLES["BAO_CAO"].BC42]),
    accessRoles: [ROLES["BAO_CAO"].BC42],
    path: "/bao-cao/bc-42",
    exact: true,
  },
  pc01: {
    component: Page(PC01, [ROLES["BAO_CAO"].PC01]),
    accessRoles: [ROLES["BAO_CAO"].PC01],
    path: "/bao-cao/pc-01",
    exact: true,
  },
  pc02: {
    component: Page(PC02, [ROLES["BAO_CAO"].PC02]),
    accessRoles: [ROLES["BAO_CAO"].PC02],
    path: "/bao-cao/pc-02",
    exact: true,
  },
  pc04: {
    component: Page(PC04, [ROLES["BAO_CAO"].PC04]),
    accessRoles: [ROLES["BAO_CAO"].PC04],
    path: "/bao-cao/pc-04",
    exact: true,
  },
  thuTienNb: {
    component: Page(TongHopThuTienNb, [ROLES["BAO_CAO"].TONG_HOP_THU_TIEN_NB]),
    accessRoles: [],
    path: "/bao-cao/tong-hop-thu-tien-nb",
    exact: true,
  },
  tc01_1: {
    component: Page(TC01_1, [ROLES["BAO_CAO"].TC01_1]),
    accessRoles: [ROLES["BAO_CAO"].TC01_1],
    path: "/bao-cao/tc01_1",
    exact: true,
  },
  tc01_2: {
    component: Page(TC01_2, [ROLES["BAO_CAO"].TC01_2]),
    accessRoles: [ROLES["BAO_CAO"].TC01_2],
    path: "/bao-cao/tc01_2",
    exact: true,
  },
  tc01_3: {
    component: Page(TC01_3, [ROLES["BAO_CAO"].TC01_3]),
    accessRoles: [ROLES["BAO_CAO"].TC01_3],
    path: "/bao-cao/tc01_3",
    exact: true,
  },
  tc01_4: {
    component: Page(TC01_4, [ROLES["BAO_CAO"].TC01_4]),
    accessRoles: [ROLES["BAO_CAO"].TC01_4],
    path: "/bao-cao/tc01_4",
    exact: true,
  },
  tc01_5: {
    component: Page(TC01_5, [ROLES["BAO_CAO"].TC01_5]),
    accessRoles: [ROLES["BAO_CAO"].TC01_5],
    path: "/bao-cao/tc01_5",
    exact: true,
  },
  tc01_6: {
    component: Page(TC01_6, [ROLES["BAO_CAO"].TC01_6]),
    accessRoles: [ROLES["BAO_CAO"].TC01_6],
    path: "/bao-cao/tc01_6",
    exact: true,
  },
  tc01_7: {
    component: Page(TC01_7, [ROLES["BAO_CAO"].TC01_7]),
    accessRoles: [ROLES["BAO_CAO"].TC01_7],
    path: "/bao-cao/tc01_7",
    exact: true,
  },
  tc01_8: {
    component: Page(TC01_8, [ROLES["BAO_CAO"].TC01_8]),
    accessRoles: [ROLES["BAO_CAO"].TC01_8],
    path: "/bao-cao/tc01_8",
    exact: true,
  },
  tc01_9: {
    component: Page(TC01_9, [ROLES["BAO_CAO"].TC01_9]),
    accessRoles: [ROLES["BAO_CAO"].TC01_9],
    path: "/bao-cao/tc01_9",
    exact: true,
  },
  tc01_10: {
    component: Page(TC01_10, [ROLES["BAO_CAO"].TC01_10]),
    accessRoles: [ROLES["BAO_CAO"].TC01_10],
    path: "/bao-cao/tc01_10",
    exact: true,
  },
  tc01_11: {
    component: Page(TC01_11, [ROLES["BAO_CAO"].TC01_11]),
    accessRoles: [ROLES["BAO_CAO"].TC01_11],
    path: "/bao-cao/tc01_11",
    exact: true,
  },
  tc01_12: {
    component: Page(TC01_12, [ROLES["BAO_CAO"].TC01_12]),
    accessRoles: [ROLES["BAO_CAO"].TC01_12],
    path: "/bao-cao/tc01_12",
    exact: true,
  },
  tc01_13: {
    component: Page(TC01_13, [ROLES["BAO_CAO"].TC01_13]),
    accessRoles: [ROLES["BAO_CAO"].TC01_13],
    path: "/bao-cao/tc01_13",
    exact: true,
  },
  tc01_14: {
    component: Page(TC01_14, [ROLES["BAO_CAO"].TC01_14]),
    accessRoles: [ROLES["BAO_CAO"].TC01_14],
    path: "/bao-cao/tc01_14",
    exact: true,
  },
  tc01_15: {
    component: Page(TC01_15, [ROLES["BAO_CAO"].TC01_15]),
    accessRoles: [ROLES["BAO_CAO"].TC01_15],
    path: "/bao-cao/tc01_15",
    exact: true,
  },
  tc01_16: {
    component: Page(TC01_16, [ROLES["BAO_CAO"].TC01_16]),
    accessRoles: [ROLES["BAO_CAO"].TC01_16],
    path: "/bao-cao/tc01_16",
    exact: true,
  },
  tc02: {
    component: Page(TC02, [ROLES["BAO_CAO"].TC02]),
    accessRoles: [],
    path: "/bao-cao/tong-hop-chi-tiet-thu-chi-theo-thu-ngan",
    exact: true,
  },
  tc02_1: {
    component: Page(TC02_1, [ROLES["BAO_CAO"].TC02_1]),
    accessRoles: [],
    path: "/bao-cao/tc02_1",
    exact: true,
  },
  tc02_2: {
    component: Page(TC02_2, [ROLES["BAO_CAO"].TC02_2]),
    accessRoles: [],
    path: "/bao-cao/tc02_2",
    exact: true,
  },
  tc03: {
    component: Page(TC03, [ROLES["BAO_CAO"].TC03]),
    accessRoles: [],
    path: "/bao-cao/tc03",
    exact: true,
  },
  tc03_2: {
    component: Page(TC03_2, [ROLES["BAO_CAO"].TC03_2]),
    accessRoles: [],
    path: "/bao-cao/tc03_2",
    exact: true,
  },
  tc04: {
    component: Page(TC04, [ROLES["BAO_CAO"].TC04]),
    accessRoles: [],
    path: "/bao-cao/tc-04",
    exact: true,
  },
  tc05: {
    component: Page(TC05, [ROLES["BAO_CAO"].TC05]),
    accessRoles: [],
    path: "/bao-cao/tc05",
    exact: true,
  },
  tc06: {
    component: Page(TC06, [ROLES["BAO_CAO"].TC06]),
    accessRoles: [],
    path: "/bao-cao/tc06",
    exact: true,
  },
  tc06_1: {
    component: Page(TC06_1, [ROLES["BAO_CAO"].TC06_1]),
    accessRoles: [ROLES["BAO_CAO"].TC06_1],
    path: "/bao-cao/tc06-1",
    exact: true,
  },
  tc06_4: {
    component: Page(TC06_4, [ROLES["BAO_CAO"].TC06_4]),
    accessRoles: [ROLES["BAO_CAO"].TC06_4],
    path: "/bao-cao/tc06-4",
    exact: true,
  },
  tc06_5: {
    component: Page(TC06_5, [ROLES["BAO_CAO"].TC06_5]),
    accessRoles: [ROLES["BAO_CAO"].TC06_5],
    path: "/bao-cao/tc06-5",
    exact: true,
  },
  tc07: {
    component: Page(TC07, [ROLES["BAO_CAO"].TC07]),
    accessRoles: [],
    path: "/bao-cao/tc07",
    exact: true,
  },
  tc08: {
    component: Page(TC08, [ROLES["BAO_CAO"].TC08]),
    accessRoles: [],
    path: "/bao-cao/tc08",
    exact: true,
  },
  tc09: {
    component: Page(TC09, [ROLES["BAO_CAO"].TC09]),
    accessRoles: [],
    path: "/bao-cao/tc09",
    exact: true,
  },
  tc10: {
    component: Page(TC10, [ROLES["BAO_CAO"].TC10]),
    accessRoles: [],
    path: "/bao-cao/tc10",
    exact: true,
  },
  tc11: {
    component: Page(TC11, [ROLES["BAO_CAO"].TC11]),
    accessRoles: [],
    path: "/bao-cao/tc11",
    exact: true,
  },
  tc12: {
    component: Page(TC12, [ROLES["BAO_CAO"].TC12]),
    accessRoles: [],
    path: "/bao-cao/tc12",
    exact: true,
  },
  tc12_1: {
    component: Page(TC12_1, [
      ROLES["BAO_CAO"].TC12_1_KHOA_THEO_TAI_KHOAN,
      ROLES["BAO_CAO"].TC12_1_TAT_CA_KHOA,
    ]),
    accessRoles: [],
    path: "/bao-cao/tc12_1",
    exact: true,
  },
  tc13: {
    component: Page(TC13, [ROLES["BAO_CAO"].TC13]),
    accessRoles: [],
    path: "/bao-cao/tc13",
    exact: true,
  },
  tc13_1: {
    component: Page(TC13_1, [ROLES["BAO_CAO"].TC13_1]),
    accessRoles: [],
    path: "/bao-cao/tc13_1",
    exact: true,
  },
  tc13_2: {
    component: Page(TC13_2, [ROLES["BAO_CAO"].TC13_2]),
    accessRoles: [],
    path: "/bao-cao/tc13_2",
    exact: true,
  },
  tc13_3: {
    component: Page(TC13_3, [ROLES["BAO_CAO"].TC13_3]),
    accessRoles: [],
    path: "/bao-cao/tc13_3",
    exact: true,
  },
  tc14: {
    component: Page(TC14, [ROLES["BAO_CAO"].TC14]),
    accessRoles: [ROLES["BAO_CAO"].TC14],
    path: "/bao-cao/tc14",
    exact: true,
  },
  tc14_1: {
    component: Page(TC14_1, [ROLES["BAO_CAO"].TC14_1]),
    accessRoles: [ROLES["BAO_CAO"].TC14_1],
    path: "/bao-cao/tc14_1",
    exact: true,
  },
  tc15: {
    component: Page(TC15, [ROLES["BAO_CAO"].TC15]),
    accessRoles: [ROLES["BAO_CAO"].TC15],
    path: "/bao-cao/tc15",
    exact: true,
  },
  tc15_1: {
    component: Page(TC15_1, [ROLES["BAO_CAO"].TC15_1]),
    accessRoles: [ROLES["BAO_CAO"].TC15_1],
    path: "/bao-cao/tc15_1",
    exact: true,
  },
  tc15_2: {
    component: Page(TC15_2, [ROLES["BAO_CAO"].TC15_2]),
    accessRoles: [ROLES["BAO_CAO"].TC15_2],
    path: "/bao-cao/tc15_2",
    exact: true,
  },
  tc15_3: {
    component: Page(TC15_3, [ROLES["BAO_CAO"].TC15_3]),
    accessRoles: [ROLES["BAO_CAO"].TC15_3],
    path: "/bao-cao/tc15_3",
    exact: true,
  },
  tc15_4: {
    component: Page(TC15_4, [ROLES["BAO_CAO"].TC15_4]),
    accessRoles: [ROLES["BAO_CAO"].TC15_4],
    path: "/bao-cao/tc15_4",
    exact: true,
  },
  tc15_5: {
    component: Page(TC15_5, [ROLES["BAO_CAO"].TC15_5]),
    accessRoles: [],
    path: "/bao-cao/tc15_5",
    exact: true,
  },
  tc16: {
    component: Page(TC16, [ROLES["BAO_CAO"].TC16]),
    accessRoles: [ROLES["BAO_CAO"].TC16],
    path: "/bao-cao/tc16",
    exact: true,
  },
  tc16_1: {
    component: Page(TC16_1, [ROLES["BAO_CAO"].TC16_1]),
    accessRoles: [ROLES["BAO_CAO"].TC16_1],
    path: "/bao-cao/tc16_1",
    exact: true,
  },
  tc17: {
    component: Page(TC17, [ROLES["BAO_CAO"].TC17]),
    accessRoles: [ROLES["BAO_CAO"].TC17],
    path: "/bao-cao/tc17",
    exact: true,
  },
  tc17_1: {
    component: Page(TC17_1, [ROLES["BAO_CAO"].TC17_1]),
    accessRoles: [ROLES["BAO_CAO"].TC17_1],
    path: "/bao-cao/tc17_1",
    exact: true,
  },
  tc17_2: {
    component: Page(TC17_2, [ROLES["BAO_CAO"].TC17_2]),
    accessRoles: [ROLES["BAO_CAO"].TC17_2],
    path: "/bao-cao/tc17_2",
    exact: true,
  },
  tc17_3: {
    component: Page(TC17_3, [ROLES["BAO_CAO"].TC17_3]),
    accessRoles: [],
    path: "/bao-cao/tc17_3",
    exact: true,
  },
  tc18: {
    component: Page(TC18, [ROLES["BAO_CAO"].TC18]),
    accessRoles: [ROLES["BAO_CAO"].TC18],
    path: "/bao-cao/tc18",
    exact: true,
  },
  tc18_1: {
    component: Page(TC18_1, [ROLES["BAO_CAO"].TC18_1]),
    accessRoles: [ROLES["BAO_CAO"].TC18_1],
    path: "/bao-cao/tc18_1",
    exact: true,
  },
  tc18_2: {
    component: Page(TC18_2, [ROLES["BAO_CAO"].TC18_2]),
    accessRoles: [ROLES["BAO_CAO"].TC18_2],
    path: "/bao-cao/tc18_2",
    exact: true,
  },
  tc19: {
    component: Page(TC19, [ROLES["BAO_CAO"].TC19]),
    accessRoles: [],
    path: "/bao-cao/tc19",
    exact: true,
  },
  tc20: {
    component: Page(TC20, [ROLES["BAO_CAO"].TC20]),
    accessRoles: [ROLES["BAO_CAO"].TC20],
    path: "/bao-cao/tc20",
    exact: true,
  },
  tc20_1: {
    component: Page(TC20_1, [ROLES["BAO_CAO"].TC20_1]),
    accessRoles: [ROLES["BAO_CAO"].TC20_1],
    path: "/bao-cao/tc20.1",
    exact: true,
  },
  tc21: {
    component: Page(TC21, [ROLES["BAO_CAO"].TC21]),
    accessRoles: [ROLES["BAO_CAO"].TC21],
    path: "/bao-cao/tc21",
    exact: true,
  },
  tc21_1: {
    component: Page(TC21_1, [ROLES["BAO_CAO"].TC21_1]),
    accessRoles: [ROLES["BAO_CAO"].TC21_1],
    path: "/bao-cao/tc21_1",
    exact: true,
  },
  tc21_2: {
    component: Page(TC21_2, [ROLES["BAO_CAO"].TC21_2]),
    accessRoles: [ROLES["BAO_CAO"].TC21_2],
    path: "/bao-cao/tc21_2",
    exact: true,
  },
  tc21_3: {
    component: Page(TC21_3, [ROLES["BAO_CAO"].TC21_3]),
    accessRoles: [ROLES["BAO_CAO"].TC21_3],
    path: "/bao-cao/tc21_3",
    exact: true,
  },
  tc21_4: {
    component: Page(TC21_4, [ROLES["BAO_CAO"].TC21_4]),
    accessRoles: [ROLES["BAO_CAO"].TC21_4],
    path: "/bao-cao/tc21_4",
    exact: true,
  },
  tc22: {
    component: Page(TC22, [ROLES["BAO_CAO"].TC22]),
    accessRoles: [ROLES["BAO_CAO"].TC22],
    path: "/bao-cao/tc22",
    exact: true,
  },
  tc22_1: {
    component: Page(TC22_1, [ROLES["BAO_CAO"].TC22_1]),
    accessRoles: [ROLES["BAO_CAO"].TC22_1],
    path: "/bao-cao/tc22.1",
    exact: true,
  },
  tc22_2: {
    component: Page(TC22_2, [ROLES["BAO_CAO"].TC22_2]),
    accessRoles: [ROLES["BAO_CAO"].TC22_2],
    path: "/bao-cao/tc22.2",
    exact: true,
  },
  tc22_3: {
    component: Page(TC22_3, [ROLES["BAO_CAO"].TC22_3]),
    accessRoles: [ROLES["BAO_CAO"].TC22_3],
    path: "/bao-cao/tc22_3",
    exact: true,
  },
  tc23: {
    component: Page(TC23, [ROLES["BAO_CAO"].TC23]),
    accessRoles: [ROLES["BAO_CAO"].TC23],
    path: "/bao-cao/tc23",
    exact: true,
  },
  tc24: {
    component: Page(TC24, [ROLES["BAO_CAO"].TC24]),
    accessRoles: [ROLES["BAO_CAO"].TC24],
    path: "/bao-cao/tc24",
    exact: true,
  },
  tc25: {
    component: Page(TC25, [ROLES["BAO_CAO"].TC25]),
    accessRoles: [ROLES["BAO_CAO"].TC25],
    path: "/bao-cao/tc25",
    exact: true,
  },
  tc26: {
    component: Page(TC26, [ROLES["BAO_CAO"].TC26]),
    accessRoles: [ROLES["BAO_CAO"].TC26],
    path: "/bao-cao/tc26",
    exact: true,
  },
  tc27: {
    component: Page(TC27, [ROLES["BAO_CAO"].TC27]),
    accessRoles: [ROLES["BAO_CAO"].TC27],
    path: "/bao-cao/tc27",
    exact: true,
  },
  tc28: {
    component: Page(TC28, [ROLES["BAO_CAO"].TC28]),
    accessRoles: [ROLES["BAO_CAO"].TC28],
    path: "/bao-cao/tc28",
    exact: true,
  },
  tc28_1: {
    component: Page(TC28_1, [ROLES["BAO_CAO"].TC28_1]),
    accessRoles: [ROLES["BAO_CAO"].TC28_1],
    path: "/bao-cao/tc28_1",
    exact: true,
  },
  tc29: {
    component: Page(TC29, [ROLES["BAO_CAO"].TC29]),
    accessRoles: [ROLES["BAO_CAO"].TC29],
    path: "/bao-cao/tc29",
    exact: true,
  },
  tc29_1: {
    component: Page(TC29_1, [ROLES["BAO_CAO"].TC29_1]),
    accessRoles: [ROLES["BAO_CAO"].TC29_1],
    path: "/bao-cao/tc29_1",
    exact: true,
  },
  tc29_2: {
    component: Page(TC29_2, [ROLES["BAO_CAO"].TC29_2]),
    accessRoles: [ROLES["BAO_CAO"].TC29_2],
    path: "/bao-cao/tc29_2",
    exact: true,
  },
  tc31: {
    component: Page(TC31, [ROLES["BAO_CAO"].TC31]),
    accessRoles: [ROLES["BAO_CAO"].TC31],
    path: "/bao-cao/tc31",
    exact: true,
  },
  tc33: {
    component: Page(TC33, [ROLES["BAO_CAO"].TC33]),
    accessRoles: [ROLES["BAO_CAO"].TC33],
    path: "/bao-cao/tc33",
    exact: true,
  },
  tc34: {
    component: Page(TC34, [ROLES["BAO_CAO"].TC34]),
    accessRoles: [ROLES["BAO_CAO"].TC34],
    path: "/bao-cao/tc34",
    exact: true,
  },
  tc36: {
    component: Page(TC36, [ROLES["BAO_CAO"].TC36]),
    accessRoles: [ROLES["BAO_CAO"].TC36],
    path: "/bao-cao/tc36",
    exact: true,
  },
  tc37: {
    component: Page(TC37, [ROLES["BAO_CAO"].TC37]),
    accessRoles: [ROLES["BAO_CAO"].TC37],
    path: "/bao-cao/tc37",
    exact: true,
  },
  tc38: {
    component: Page(TC38, [ROLES["BAO_CAO"].TC38]),
    accessRoles: [ROLES["BAO_CAO"].TC38],
    path: "/bao-cao/tc38",
    exact: true,
  },
  tc39: {
    component: Page(TC39, [ROLES["BAO_CAO"].TC39]),
    accessRoles: [ROLES["BAO_CAO"].TC39],
    path: "/bao-cao/tc39",
    exact: true,
  },
  tc40: {
    component: Page(TC40, [ROLES["BAO_CAO"].TC40]),
    accessRoles: [ROLES["BAO_CAO"].TC40],
    path: "/bao-cao/tc40",
    exact: true,
  },
  tc41: {
    component: Page(TC41, [ROLES["BAO_CAO"].TC41]),
    accessRoles: [ROLES["BAO_CAO"].TC41],
    path: "/bao-cao/tc41",
    exact: true,
  },
  tc41_1: {
    component: Page(TC41_1, [ROLES["BAO_CAO"].TC41_1]),
    accessRoles: [ROLES["BAO_CAO"].TC41_1],
    path: "/bao-cao/tc41_1",
    exact: true,
  },
  tc42: {
    component: Page(TC42, [ROLES["BAO_CAO"].TC42]),
    accessRoles: [ROLES["BAO_CAO"].TC42],
    path: "/bao-cao/tc42",
    exact: true,
  },
  tc42_1: {
    component: Page(TC42_1, [ROLES["BAO_CAO"].TC42_1]),
    accessRoles: [ROLES["BAO_CAO"].TC42_1],
    path: "/bao-cao/tc42_1",
    exact: true,
  },
  tc42_2: {
    component: Page(TC42_2, [ROLES["BAO_CAO"].TC42_2]),
    accessRoles: [ROLES["BAO_CAO"].TC42_2],
    path: "/bao-cao/tc42_2",
    exact: true,
  },
  tc42_3: {
    component: Page(TC42_3, [ROLES["BAO_CAO"].TC42_3]),
    accessRoles: [ROLES["BAO_CAO"].TC42_3],
    path: "/bao-cao/tc42_3",
    exact: true,
  },
  tc42_4: {
    component: Page(TC42_4, [ROLES["BAO_CAO"].TC42_4]),
    accessRoles: [ROLES["BAO_CAO"].TC42_4],
    path: "/bao-cao/tc42_4",
    exact: true,
  },
  tc42_5: {
    component: Page(TC42_5, [ROLES["BAO_CAO"].TC42_5]),
    accessRoles: [ROLES["BAO_CAO"].TC42_5],
    path: "/bao-cao/tc42_5",
    exact: true,
  },
  tc42_6: {
    component: Page(TC42_6, [ROLES["BAO_CAO"].TC42_6]),
    accessRoles: [ROLES["BAO_CAO"].TC42_6],
    path: "/bao-cao/tc42_6",
    exact: true,
  },
  tc46: {
    component: Page(TC46, [ROLES["BAO_CAO"].TC46]),
    accessRoles: [ROLES["BAO_CAO"].TC46],
    path: "/bao-cao/tc46",
    exact: true,
  },
  tc48: {
    component: Page(TC48, [ROLES["BAO_CAO"].TC48]),
    accessRoles: [ROLES["BAO_CAO"].TC48],
    path: "/bao-cao/tc48",
    exact: true,
  },
  tc49: {
    component: Page(TC49, [ROLES["BAO_CAO"].TC49]),
    accessRoles: [ROLES["BAO_CAO"].TC49],
    path: "/bao-cao/tc49",
    exact: true,
  },
  tc50: {
    component: Page(TC50, [ROLES["BAO_CAO"].TC50]),
    accessRoles: [ROLES["BAO_CAO"].TC50],
    path: "/bao-cao/tc50",
    exact: true,
  },
  tc51: {
    component: Page(TC51, [ROLES["BAO_CAO"].TC51]),
    accessRoles: [ROLES["BAO_CAO"].TC51],
    path: "/bao-cao/tc51",
    exact: true,
  },
  tc51_1: {
    component: Page(TC51_1, [ROLES["BAO_CAO"].TC51_1]),
    accessRoles: [ROLES["BAO_CAO"].TC51_1],
    path: "/bao-cao/tc51_1",
    exact: true,
  },
  tc53: {
    component: Page(TC53, [ROLES["BAO_CAO"].TC53]),
    accessRoles: [ROLES["BAO_CAO"].TC53],
    path: "/bao-cao/tc53",
    exact: true,
  },
  tc54: {
    component: Page(TC54, [ROLES["BAO_CAO"].TC54]),
    accessRoles: [ROLES["BAO_CAO"].TC54],
    path: "/bao-cao/tc54",
    exact: true,
  },
  tc54_1: {
    component: Page(TC54_1, [ROLES["BAO_CAO"].TC54_1]),
    accessRoles: [ROLES["BAO_CAO"].TC54_1],
    path: "/bao-cao/tc54_1",
    exact: true,
  },
  tc55: {
    component: Page(TC55, [ROLES["BAO_CAO"].TC55]),
    accessRoles: [ROLES["BAO_CAO"].TC55],
    path: "/bao-cao/tc55",
    exact: true,
  },
  tc55_1: {
    component: Page(TC55_1, [ROLES["BAO_CAO"].TC55_1]),
    accessRoles: [ROLES["BAO_CAO"].TC55_1],
    path: "/bao-cao/tc55_1",
    exact: true,
  },
  tc56: {
    component: Page(TC56, [ROLES["BAO_CAO"].TC56]),
    accessRoles: [ROLES["BAO_CAO"].TC56],
    path: "/bao-cao/tc56",
    exact: true,
  },
  tc57: {
    component: Page(TC57, [ROLES["BAO_CAO"].TC57]),
    accessRoles: [ROLES["BAO_CAO"].TC57],
    path: "/bao-cao/tc57",
    exact: true,
  },
  tc57_1: {
    component: Page(TC57_1, [ROLES["BAO_CAO"].TC57_1]),
    accessRoles: [ROLES["BAO_CAO"].TC57_1],
    path: "/bao-cao/tc57_1",
    exact: true,
  },
  tc58: {
    component: Page(TC58, [ROLES["BAO_CAO"].TC58]),
    accessRoles: [ROLES["BAO_CAO"].TC58],
    path: "/bao-cao/tc58",
    exact: true,
  },
  tc58_1: {
    component: Page(TC58_1, [ROLES["BAO_CAO"].TC58_1]),
    accessRoles: [ROLES["BAO_CAO"].TC58_1],
    path: "/bao-cao/tc58_1",
    exact: true,
  },
  tc59: {
    component: Page(TC59, [ROLES["BAO_CAO"].TC59]),
    accessRoles: [ROLES["BAO_CAO"].TC59],
    path: "/bao-cao/tc59",
    exact: true,
  },
  tc59_1: {
    component: Page(TC59_1, [ROLES["BAO_CAO"].TC59_1]),
    accessRoles: [ROLES["BAO_CAO"].TC59_1],
    path: "/bao-cao/tc59_1",
    exact: true,
  },
  tc59_2: {
    component: Page(TC59_2, [ROLES["BAO_CAO"].TC59_2]),
    accessRoles: [ROLES["BAO_CAO"].TC59_2],
    path: "/bao-cao/tc59_2",
    exact: true,
  },
  tc59_3: {
    component: Page(TC59_3, [ROLES["BAO_CAO"].TC59_3]),
    accessRoles: [ROLES["BAO_CAO"].TC59_3],
    path: "/bao-cao/tc59_3",
    exact: true,
  },
  tc60: {
    component: Page(TC60, [ROLES["BAO_CAO"].TC60]),
    accessRoles: [ROLES["BAO_CAO"].TC60],
    path: "/bao-cao/tc60",
    exact: true,
  },
  tc61: {
    component: Page(TC61, [ROLES["BAO_CAO"].TC61]),
    accessRoles: [ROLES["BAO_CAO"].TC61],
    path: "/bao-cao/tc61",
    exact: true,
  },
  tc62: {
    component: Page(TC62, [ROLES["BAO_CAO"].TC62]),
    accessRoles: [ROLES["BAO_CAO"].TC62],
    path: "/bao-cao/tc62",
    exact: true,
  },
  tc63: {
    component: Page(TC63, [ROLES["BAO_CAO"].TC63]),
    accessRoles: [ROLES["BAO_CAO"].TC63],
    path: "/bao-cao/tc63",
    exact: true,
  },
  tc63_1: {
    component: Page(TC63_1, [ROLES["BAO_CAO"].TC63_1]),
    accessRoles: [ROLES["BAO_CAO"].TC63_1],
    path: "/bao-cao/tc63_1",
    exact: true,
  },
  tc64: {
    component: Page(TC64, [ROLES["BAO_CAO"].TC64]),
    accessRoles: [ROLES["BAO_CAO"].TC64],
    path: "/bao-cao/tc64",
    exact: true,
  },
  tc64_1: {
    component: Page(TC64_1, [ROLES["BAO_CAO"].TC64_1]),
    accessRoles: [ROLES["BAO_CAO"].TC64_1],
    path: "/bao-cao/tc64_1",
    exact: true,
  },
  tc64_2: {
    component: Page(TC64_2, [ROLES["BAO_CAO"].TC64_2]),
    accessRoles: [ROLES["BAO_CAO"].TC64_2],
    path: "/bao-cao/tc64_2",
    exact: true,
  },
  tc66: {
    component: Page(TC66, [ROLES["BAO_CAO"].TC66]),
    accessRoles: [ROLES["BAO_CAO"].TC66],
    path: "/bao-cao/tc66",
    exact: true,
  },
  tc67: {
    component: Page(TC67, [ROLES["BAO_CAO"].TC67]),
    accessRoles: [ROLES["BAO_CAO"].TC67],
    path: "/bao-cao/tc67",
    exact: true,
  },
  tc67_1: {
    component: Page(TC67_1, [ROLES["BAO_CAO"].TC67_1]),
    accessRoles: [ROLES["BAO_CAO"].TC67_1],
    path: "/bao-cao/tc67_1",
    exact: true,
  },
  tc67_2: {
    component: Page(TC67_2, [ROLES["BAO_CAO"].TC67_2]),
    accessRoles: [ROLES["BAO_CAO"].TC67_2],
    path: "/bao-cao/tc67_2",
    exact: true,
  },
  tc68: {
    component: Page(TC68, [ROLES["BAO_CAO"].TC68]),
    accessRoles: [ROLES["BAO_CAO"].TC68],
    path: "/bao-cao/tc68",
    exact: true,
  },
  tc69: {
    component: Page(TC69, [ROLES["BAO_CAO"].TC69]),
    accessRoles: [],
    path: "/bao-cao/tc69",
    exact: true,
  },
  tc69_1: {
    component: Page(TC69_1, [ROLES["BAO_CAO"].TC69_1]),
    accessRoles: [ROLES["BAO_CAO"].TC69_1],
    path: "/bao-cao/tc69_1",
    exact: true,
  },
  tc69_2: {
    component: Page(TC69_2, [ROLES["BAO_CAO"].TC69_2]),
    accessRoles: [ROLES["BAO_CAO"].TC69_2],
    path: "/bao-cao/tc69_2",
    exact: true,
  },
  tc70: {
    component: Page(TC70, [ROLES["BAO_CAO"].TC70]),
    accessRoles: [ROLES["BAO_CAO"].TC70],
    path: "/bao-cao/tc70",
    exact: true,
  },
  tc71: {
    component: Page(TC71, [ROLES["BAO_CAO"].TC71]),
    accessRoles: [ROLES["BAO_CAO"].TC71],
    path: "/bao-cao/tc71",
    exact: true,
  },
  tc72: {
    component: Page(TC72, [ROLES["BAO_CAO"].TC72]),
    accessRoles: [ROLES["BAO_CAO"].TC72],
    path: "/bao-cao/tc72",
    exact: true,
  },
  tc73: {
    component: Page(TC73, [ROLES["BAO_CAO"].TC73]),
    accessRoles: [ROLES["BAO_CAO"].TC73],
    path: "/bao-cao/tc73",
    exact: true,
  },
  tc73_1: {
    component: Page(TC73_1, [ROLES["BAO_CAO"].TC73_1]),
    accessRoles: [ROLES["BAO_CAO"].TC73_1],
    path: "/bao-cao/tc73_1",
    exact: true,
  },
  tc74: {
    component: Page(TC74, [ROLES["BAO_CAO"].TC74]),
    accessRoles: [ROLES["BAO_CAO"].TC74],
    path: "/bao-cao/tc74",
    exact: true,
  },
  tc75: {
    component: Page(TC75, [ROLES["BAO_CAO"].TC75]),
    accessRoles: [ROLES["BAO_CAO"].TC75],
    path: "/bao-cao/tc75",
    exact: true,
  },
  tc75_1: {
    component: Page(TC75_1, [ROLES["BAO_CAO"].TC75_1]),
    accessRoles: [ROLES["BAO_CAO"].TC75_1],
    path: "/bao-cao/tc75_1",
    exact: true,
  },
  tc76: {
    component: Page(TC76, [ROLES["BAO_CAO"].TC76]),
    accessRoles: [ROLES["BAO_CAO"].TC76],
    path: "/bao-cao/tc76",
    exact: true,
  },
  tc77: {
    component: Page(TC77, [ROLES["BAO_CAO"].TC77]),
    accessRoles: [ROLES["BAO_CAO"].TC77],
    path: "/bao-cao/tc77",
    exact: true,
  },
  tc79: {
    component: Page(TC79, [ROLES["BAO_CAO"].TC79]),
    accessRoles: [ROLES["BAO_CAO"].TC79],
    path: "/bao-cao/tc79",
    exact: true,
  },
  tc79_1: {
    component: Page(TC79_1, [ROLES["BAO_CAO"].TC79_1]),
    accessRoles: [ROLES["BAO_CAO"].TC79_1],
    path: "/bao-cao/tc79_1",
    exact: true,
  },
  tc80: {
    component: Page(TC80, [ROLES["BAO_CAO"].TC80]),
    accessRoles: [ROLES["BAO_CAO"].TC80],
    path: "/bao-cao/tc80",
    exact: true,
  },
  tc81: {
    component: Page(TC81, [ROLES["BAO_CAO"].TC81]),
    accessRoles: [ROLES["BAO_CAO"].TC81],
    path: "/bao-cao/tc81",
    exact: true,
  },
  tc81_1: {
    component: Page(TC81_1, [ROLES["BAO_CAO"].TC81_1]),
    accessRoles: [ROLES["BAO_CAO"].TC81_1],
    path: "/bao-cao/tc81_1",
    exact: true,
  },
  tc80_1: {
    component: Page(TC80_1, [ROLES["BAO_CAO"].TC80_1]),
    accessRoles: [ROLES["BAO_CAO"].TC80_1],
    path: "/bao-cao/tc80_1",
    exact: true,
  },
  tc80_2: {
    component: Page(TC80_2, [ROLES["BAO_CAO"].TC80_2]),
    accessRoles: [ROLES["BAO_CAO"].TC80_2],
    path: "/bao-cao/tc80_2",
    exact: true,
  },
  nhaCungCap: {
    component: Page(NhaCungCap, [ROLES["BAO_CAO"].NHAP_THEO_NHA_CC]),
    accessRoles: [],
    path: "/bao-cao/nhap-theo-nha-cung-cap",
    exact: true,
  },
  hoaDonNhap: {
    component: Page(K01, [ROLES["BAO_CAO"].BANG_KE_HOA_DON_NHAP]),
    accessRoles: [],
    path: "/bao-cao/bang-ke-hoa-don-nhap",
    exact: true,
  },
  k01_2: {
    component: Page(K01_2, [ROLES["BAO_CAO"].K01_2]),
    accessRoles: [],
    path: "/bao-cao/k01_2",
    exact: true,
  },
  k01_3: {
    component: Page(K01_3, [ROLES["BAO_CAO"].K01_3]),
    accessRoles: [],
    path: "/bao-cao/k01_3",
    exact: true,
  },
  k02: {
    component: Page(K02, [ROLES["BAO_CAO"].XUAT_NHAP_TON_KHO]),
    accessRoles: [],
    path: "/bao-cao/k02",
    exact: true,
  },
  k02_1: {
    component: Page(K02_1, [ROLES["BAO_CAO"].K02_1]),
    accessRoles: [],
    path: "/bao-cao/k02_1",
    exact: true,
  },
  k02_2: {
    component: Page(K02_2, [ROLES["BAO_CAO"].K02_2]),
    accessRoles: [],
    path: "/bao-cao/k02_2",
    exact: true,
  },
  k02_3: {
    component: Page(K02_3, [ROLES["BAO_CAO"].K02_3]),
    accessRoles: [],
    path: "/bao-cao/k02_3",
    exact: true,
  },
  k02_4: {
    component: Page(K02_4, [ROLES["BAO_CAO"].K02_4]),
    accessRoles: [],
    path: "/bao-cao/k02_4",
    exact: true,
  },
  k02_5: {
    component: Page(K02_5, [ROLES["BAO_CAO"].K02_5]),
    accessRoles: [],
    path: "/bao-cao/k02_5",
    exact: true,
  },
  k02_6: {
    component: Page(K02_6, [ROLES["BAO_CAO"].K02_6]),
    accessRoles: [],
    path: "/bao-cao/k02_6",
    exact: true,
  },
  k02_7: {
    component: Page(K02_7, [ROLES["BAO_CAO"].XUAT_NHAP_TON_KHO]),
    accessRoles: [],
    path: "/bao-cao/k02_7",
    exact: true,
  },
  k02_8: {
    component: Page(K02_8, [ROLES["BAO_CAO"].K02_8]),
    accessRoles: [],
    path: "/bao-cao/k02_8",
    exact: true,
  },
  k02_10: {
    component: Page(K02_10, [ROLES["BAO_CAO"].K02_10]),
    accessRoles: [],
    path: "/bao-cao/k02_10",
    exact: true,
  },
  k03: {
    component: Page(K03, [ROLES["BAO_CAO"].CHI_TIET_XUAT_KHO]),
    accessRoles: [],
    path: "/bao-cao/k03",
    exact: true,
  },
  k04: {
    component: Page(K04, [ROLES["BAO_CAO"].THE_KHO]),
    accessRoles: [],
    path: "/bao-cao/k04",
    exact: true,
  },
  k04_1: {
    component: Page(K04_1, [ROLES["BAO_CAO"].K04_1]),
    accessRoles: [],
    path: "/bao-cao/k04_1",
    exact: true,
  },
  k04_2: {
    component: Page(K04_2, [ROLES["BAO_CAO"].K04_2]),
    accessRoles: [ROLES["BAO_CAO"].K04_2],
    path: "/bao-cao/k04_2",
    exact: true,
  },
  k04_3: {
    component: Page(K04_3, [ROLES["BAO_CAO"].K04_3]),
    accessRoles: [ROLES["BAO_CAO"].K04_3],
    path: "/bao-cao/k04_3",
    exact: true,
  },
  k04_4: {
    component: Page(K04_4, [ROLES["BAO_CAO"].K04_4]),
    accessRoles: [],
    path: "/bao-cao/k04_4",
    exact: true,
  },
  k05: {
    component: Page(K05, [ROLES["BAO_CAO"].CHI_TIET_NHAP_KHO]),
    accessRoles: [],
    path: "/bao-cao/k05",
    exact: true,
  },
  k05_1: {
    component: Page(K05_1, [ROLES["BAO_CAO"].K05_1]),
    accessRoles: [],
    path: "/bao-cao/k05_1",
    exact: true,
  },
  k07: {
    component: Page(K07, [ROLES["BAO_CAO"].K07]),
    accessRoles: [],
    path: "/bao-cao/k07",
    exact: true,
  },
  k07_1: {
    component: Page(K07_1, [ROLES["BAO_CAO"].K07_1]),
    accessRoles: [ROLES["BAO_CAO"].K07_1],
    path: "/bao-cao/k07_1",
    exact: true,
  },
  k08: {
    component: Page(K08, [ROLES["BAO_CAO"].K08]),
    accessRoles: [],
    path: "/bao-cao/k08",
    exact: true,
  },
  k10: {
    component: Page(K10, [ROLES["BAO_CAO"].K10]),
    accessRoles: [],
    path: "/bao-cao/k10",
    exact: true,
  },
  k11: {
    component: Page(K11, [ROLES["BAO_CAO"].K11]),
    accessRoles: [],
    path: "/bao-cao/k11",
    exact: true,
  },
  k12: {
    component: Page(K12, [ROLES["BAO_CAO"].K12]),
    accessRoles: [],
    path: "/bao-cao/k12",
    exact: true,
  },
  k13: {
    component: Page(K13, [ROLES["BAO_CAO"].K13]),
    accessRoles: [],
    path: "/bao-cao/k13",
    exact: true,
  },
  k14: {
    component: Page(K14, [ROLES["BAO_CAO"].K14]),
    accessRoles: [ROLES["BAO_CAO"].K14],
    path: "/bao-cao/k14",
    exact: true,
  },
  K14_1: {
    component: Page(K14_1, [ROLES["BAO_CAO"].K14_1]),
    accessRoles: [ROLES["BAO_CAO"].K14_1],
    path: "/bao-cao/k14_1",
    exact: true,
  },
  K14_2: {
    component: Page(K14_2, [ROLES["BAO_CAO"].K14_2]),
    accessRoles: [ROLES["BAO_CAO"].K14_2],
    path: "/bao-cao/k14_2",
    exact: true,
  },
  K15: {
    component: Page(K15, [ROLES["BAO_CAO"].K15]),
    accessRoles: [ROLES["BAO_CAO"].K15],
    path: "/bao-cao/k15",
    exact: true,
  },
  kvt04: {
    component: Page(Kvt04, [ROLES["BAO_CAO"].KVT04]),
    accessRoles: [],
    path: "/bao-cao/kvt04",
    exact: true,
  },
  k20: {
    component: Page(K20, [ROLES["BAO_CAO"].K20]),
    accessRoles: [ROLES["BAO_CAO"].K20],
    path: "/bao-cao/k20",
    exact: true,
  },
  k20_1: {
    component: Page(K20_1, [ROLES["BAO_CAO"].K20_1]),
    accessRoles: [ROLES["BAO_CAO"].K20_1],
    path: "/bao-cao/k20_1",
    exact: true,
  },
  k20_2: {
    component: Page(K20_2, [ROLES["BAO_CAO"].K20_2]),
    accessRoles: [ROLES["BAO_CAO"].K20_2],
    path: "/bao-cao/k20_2",
    exact: true,
  },
  k20_3: {
    component: Page(K20_3, [ROLES["BAO_CAO"].K20_3]),
    accessRoles: [ROLES["BAO_CAO"].K20_3],
    path: "/bao-cao/k20_3",
    exact: true,
  },
  k21: {
    component: Page(K21, [ROLES["BAO_CAO"].K21]),
    accessRoles: [ROLES["BAO_CAO"].K21],
    path: "/bao-cao/k21",
    exact: true,
  },
  k22: {
    component: Page(K22, [ROLES["BAO_CAO"].K22]),
    accessRoles: [],
    path: "/bao-cao/k22",
    exact: true,
  },
  k23: {
    component: Page(K23, [ROLES["BAO_CAO"].K23]),
    accessRoles: [],
    path: "/bao-cao/k23",
    exact: true,
  },
  k24: {
    component: Page(K24, [ROLES["BAO_CAO"].K24]),
    accessRoles: [ROLES["BAO_CAO"].K24],
    path: "/bao-cao/k24",
    exact: true,
  },
  k25: {
    component: Page(K25, [ROLES["BAO_CAO"].K25]),
    accessRoles: [ROLES["BAO_CAO"].K25],
    path: "/bao-cao/k25",
    exact: true,
  },
  k26: {
    component: Page(K26, [ROLES["BAO_CAO"].K26]),
    accessRoles: [ROLES["BAO_CAO"].K26],
    path: "/bao-cao/k26",
    exact: true,
  },
  k27: {
    component: Page(K27, [ROLES["BAO_CAO"].K27]),
    accessRoles: [ROLES["BAO_CAO"].K27],
    path: "/bao-cao/k27",
    exact: true,
  },
  k28: {
    component: Page(K28, [ROLES["BAO_CAO"].K28]),
    accessRoles: [ROLES["BAO_CAO"].K28],
    path: "/bao-cao/k28",
    exact: true,
  },
  k29: {
    component: Page(K29, [ROLES["BAO_CAO"].K29]),
    accessRoles: [],
    path: "/bao-cao/k29",
    exact: true,
  },
  k30: {
    component: Page(K30, [ROLES["BAO_CAO"].K30]),
    accessRoles: [ROLES["BAO_CAO"].K30],
    path: "/bao-cao/k30",
    exact: true,
  },
  k31: {
    component: Page(K31, [ROLES["BAO_CAO"].K31]),
    accessRoles: [],
    path: "/bao-cao/k31",
    exact: true,
  },
  k32: {
    component: Page(K32, [ROLES["BAO_CAO"].K32]),
    accessRoles: [ROLES["BAO_CAO"].K32],
    path: "/bao-cao/k32",
    exact: true,
  },
  k33: {
    component: Page(K33, [ROLES["BAO_CAO"].K33]),
    accessRoles: [],
    path: "/bao-cao/k33",
    exact: true,
  },
  k34: {
    component: Page(K34, [ROLES["BAO_CAO"].K34]),
    accessRoles: [ROLES["BAO_CAO"].K34],
    path: "/bao-cao/k34",
    exact: true,
  },
  k35: {
    component: Page(K35, [ROLES["BAO_CAO"].K35]),
    accessRoles: [ROLES["BAO_CAO"].K35],
    path: "/bao-cao/k35",
    exact: true,
  },
  k36: {
    component: Page(K36, [ROLES["BAO_CAO"].K36]),
    accessRoles: [],
    path: "/bao-cao/k36",
    exact: true,
  },
  k37: {
    component: Page(K37, [ROLES["BAO_CAO"].K37]),
    accessRoles: [],
    path: "/bao-cao/k37",
    exact: true,
  },
  k38: {
    component: Page(K38, [ROLES["BAO_CAO"].K38]),
    accessRoles: [],
    path: "/bao-cao/k38",
    exact: true,
  },
  k39: {
    component: Page(K39, [ROLES["BAO_CAO"].K39]),
    accessRoles: [ROLES["BAO_CAO"].K39],
    path: "/bao-cao/k39",
    exact: true,
  },
  k40: {
    component: Page(K40, [ROLES["BAO_CAO"].K40]),
    accessRoles: [],
    path: "/bao-cao/k40",
    exact: true,
  },
  k42: {
    component: Page(K42, [ROLES["BAO_CAO"].K42]),
    accessRoles: [],
    path: "/bao-cao/k42",
    exact: true,
  },
  k43: {
    component: Page(K43, [ROLES["BAO_CAO"].K43]),
    accessRoles: [],
    path: "/bao-cao/k43",
    exact: true,
  },
  k44: {
    component: Page(K44, [ROLES["BAO_CAO"].K44]),
    accessRoles: [ROLES["BAO_CAO"].K44],
    path: "/bao-cao/k44",
    exact: true,
  },
  k45: {
    component: Page(K45, [ROLES["BAO_CAO"].K45]),
    accessRoles: [],
    path: "/bao-cao/k45",
    exact: true,
  },
  k46: {
    component: Page(K46, [ROLES["BAO_CAO"].K46]),
    accessRoles: [ROLES["BAO_CAO"].K46],
    path: "/bao-cao/k46",
    exact: true,
  },
  k47: {
    component: Page(K47, [ROLES["BAO_CAO"].K47]),
    accessRoles: [ROLES["BAO_CAO"].K47],
    path: "/bao-cao/k47",
    exact: true,
  },
  k48: {
    component: Page(K48, [ROLES["BAO_CAO"].K48]),
    accessRoles: [ROLES["BAO_CAO"].K48],
    path: "/bao-cao/k48",
    exact: true,
  },
  k49: {
    component: Page(K49, [ROLES["BAO_CAO"].K49]),
    accessRoles: [],
    path: "/bao-cao/k49",
    exact: true,
  },
  k50: {
    component: Page(K50, [ROLES["BAO_CAO"].K50]),
    accessRoles: [],
    path: "/bao-cao/k50",
    exact: true,
  },
  k51: {
    component: Page(K51, [ROLES["BAO_CAO"].K51]),
    accessRoles: [ROLES["BAO_CAO"].K51],
    path: "/bao-cao/k51",
    exact: true,
  },
  k52: {
    component: Page(K52, [ROLES["BAO_CAO"].K52]),
    accessRoles: [ROLES["BAO_CAO"].K52],
    path: "/bao-cao/k52",
    exact: true,
  },
  k53: {
    component: Page(K53, [ROLES["BAO_CAO"].K53]),
    accessRoles: [],
    path: "/bao-cao/k53",
    exact: true,
  },
  k54: {
    component: Page(K54, [ROLES["BAO_CAO"].K54]),
    accessRoles: [ROLES["BAO_CAO"].K54],
    path: "/bao-cao/k54",
    exact: true,
  },
  k55: {
    component: Page(K55, [ROLES["BAO_CAO"].K55]),
    accessRoles: [ROLES["BAO_CAO"].K55],
    path: "/bao-cao/k55",
    exact: true,
  },
  k56: {
    component: Page(K56, [ROLES["BAO_CAO"].K56]),
    accessRoles: [ROLES["BAO_CAO"].K56],
    path: "/bao-cao/k56",
    exact: true,
  },
  k57: {
    component: Page(K57, [ROLES["BAO_CAO"].K57]),
    accessRoles: [ROLES["BAO_CAO"].K57],
    path: "/bao-cao/k57",
    exact: true,
  },
  k58: {
    component: Page(K58, [ROLES["BAO_CAO"].K58]),
    accessRoles: [ROLES["BAO_CAO"].K58],
    path: "/bao-cao/k58",
    exact: true,
  },
  k58_1: {
    component: Page(K58_1, [ROLES["BAO_CAO"].K58_1]),
    accessRoles: [ROLES["BAO_CAO"].K58_1],
    path: "/bao-cao/k58_1",
    exact: true,
  },
  k59: {
    component: Page(K59, [ROLES["BAO_CAO"].K59]),
    accessRoles: [ROLES["BAO_CAO"].K59],
    path: "/bao-cao/k59",
    exact: true,
  },
  k60: {
    component: Page(K60, [ROLES["BAO_CAO"].K60]),
    accessRoles: [ROLES["BAO_CAO"].K60],
    path: "/bao-cao/k60",
    exact: true,
  },
  k61: {
    component: Page(K61, [ROLES["BAO_CAO"].K61]),
    accessRoles: [],
    path: "/bao-cao/k61",
    exact: true,
  },
  k62: {
    component: Page(K62, [ROLES["BAO_CAO"].K62]),
    accessRoles: [],
    path: "/bao-cao/k62",
    exact: true,
  },
  k63: {
    component: Page(K63, [ROLES["BAO_CAO"].K63]),
    accessRoles: [],
    path: "/bao-cao/k63",
    exact: true,
  },
  k64: {
    component: Page(K64, [ROLES["BAO_CAO"].K64]),
    accessRoles: [],
    path: "/bao-cao/k64",
    exact: true,
  },
  k65: {
    component: Page(K65, [ROLES["BAO_CAO"].K65]),
    accessRoles: [],
    path: "/bao-cao/k65",
    exact: true,
  },
  k66: {
    component: Page(K66, [ROLES["BAO_CAO"].K66]),
    accessRoles: [],
    path: "/bao-cao/k66",
    exact: true,
  },
  k67: {
    component: Page(K67, [ROLES["BAO_CAO"].K67]),
    accessRoles: [],
    path: "/bao-cao/k67",
    exact: true,
  },
  k68: {
    component: Page(K68, [ROLES["BAO_CAO"].K68]),
    accessRoles: [ROLES["BAO_CAO"].K68],
    path: "/bao-cao/k68",
    exact: true,
  },
  k69: {
    component: Page(K69, [ROLES["BAO_CAO"].K69]),
    accessRoles: [ROLES["BAO_CAO"].K69],
    path: "/bao-cao/k69",
    exact: true,
  },
  k70: {
    component: Page(K70, [ROLES["BAO_CAO"].K70]),
    accessRoles: [ROLES["BAO_CAO"].K70],
    path: "/bao-cao/k70",
    exact: true,
  },
  k71: {
    component: Page(K71, [ROLES["BAO_CAO"].K71]),
    accessRoles: [ROLES["BAO_CAO"].K71],
    path: "/bao-cao/k71",
    exact: true,
  },
  k72: {
    component: Page(K72, [ROLES["BAO_CAO"].K72]),
    accessRoles: [ROLES["BAO_CAO"].K72],
    path: "/bao-cao/k72",
    exact: true,
  },
  k73: {
    component: Page(K73, [ROLES["BAO_CAO"].K73]),
    accessRoles: [ROLES["BAO_CAO"].K73],
    path: "/bao-cao/k73",
    exact: true,
  },
  k74: {
    component: Page(K74, [ROLES["BAO_CAO"].K74]),
    accessRoles: [ROLES["BAO_CAO"].K74],
    path: "/bao-cao/k74",
    exact: true,
  },
  k75: {
    component: Page(K75, [ROLES["BAO_CAO"].K75]),
    accessRoles: [ROLES["BAO_CAO"].K75],
    path: "/bao-cao/k75",
    exact: true,
  },
  k76: {
    component: Page(K76, [ROLES["BAO_CAO"].K76]),
    accessRoles: [ROLES["BAO_CAO"].K76],
    path: "/bao-cao/k76",
    exact: true,
  },
  k77: {
    component: Page(K77, [ROLES["BAO_CAO"].K77]),
    accessRoles: [ROLES["BAO_CAO"].K77],
    path: "/bao-cao/k77",
    exact: true,
  },
  k78: {
    component: Page(K78, [ROLES["BAO_CAO"].K78]),
    accessRoles: [],
    path: "/bao-cao/k78",
    exact: true,
  },
  k79: {
    component: Page(K79, [ROLES["BAO_CAO"].K79]),
    accessRoles: [],
    path: "/bao-cao/k79",
    exact: true,
  },
  k80: {
    component: Page(K80, [ROLES["BAO_CAO"].K80]),
    accessRoles: [ROLES["BAO_CAO"].K80],
    path: "/bao-cao/k80",
    exact: true,
  },
  k82: {
    component: Page(K82, [ROLES["BAO_CAO"].K82]),
    accessRoles: [ROLES["BAO_CAO"].K82],
    path: "/bao-cao/k82",
    exact: true,
  },
  k84: {
    component: Page(K84, [ROLES["BAO_CAO"].K84]),
    accessRoles: [ROLES["BAO_CAO"].K84],
    path: "/bao-cao/k84",
    exact: true,
  },
  kvt01_1: {
    component: Page(KVT01_1, [ROLES["BAO_CAO"].KVT01_1]),
    accessRoles: [],
    path: "/bao-cao/kvt01_1",
    exact: true,
  },
  kvt02: {
    component: Page(KVT02, [ROLES["BAO_CAO"].KVT02]),
    accessRoles: [],
    path: "/bao-cao/kvt02",
    exact: true,
  },
  kvt03: {
    component: Page(KVT03, [ROLES["BAO_CAO"].KVT03]),
    accessRoles: [],
    path: "/bao-cao/kvt03",
    exact: true,
  },
  kvt05: {
    component: Page(KVT05, [ROLES["BAO_CAO"].KVT05]),
    accessRoles: [],
    path: "/bao-cao/kvt05",
    exact: true,
  },
  kvt06: {
    component: Page(KVT06, [ROLES["BAO_CAO"].KVT06]),
    accessRoles: [ROLES["BAO_CAO"].KVT06],
    path: "/bao-cao/kvt06",
    exact: true,
  },
  kvt07: {
    component: Page(KVT07, [ROLES["BAO_CAO"].KVT07]),
    accessRoles: [ROLES["BAO_CAO"].KVT07],
    path: "/bao-cao/kvt07",
    exact: true,
  },
  knt01: {
    component: Page(KNT01, [ROLES["BAO_CAO"].KNT01]),
    accessRoles: [],
    path: "/bao-cao/knt01",
    exact: true,
  },
  knt03: {
    component: Page(KNT03, [ROLES["BAO_CAO"].KNT03]),
    accessRoles: [],
    path: "/bao-cao/knt03",
    exact: true,
  },
  knt03_1: {
    component: Page(KNT03_1, [ROLES["BAO_CAO"].KNT03_1]),
    accessRoles: [],
    path: "/bao-cao/knt03_1",
    exact: true,
  },
  knt02: {
    component: Page(KNT02, [ROLES["BAO_CAO"].KNT02]),
    accessRoles: [],
    path: "/bao-cao/knt02",
    exact: true,
  },
  knt04: {
    component: Page(KNT04, [ROLES["BAO_CAO"].KNT04]),
    accessRoles: [],
    path: "/bao-cao/knt04",
    exact: true,
  },
  knt05: {
    component: Page(KNT05, [ROLES["BAO_CAO"].KNT05]),
    accessRoles: [],
    path: "/bao-cao/knt05",
    exact: true,
  },
  knt06: {
    component: Page(KNT06, [ROLES["BAO_CAO"].KNT06]),
    accessRoles: [],
    path: "/bao-cao/knt06",
    exact: true,
  },
  knt07: {
    component: Page(KNT07, [ROLES["BAO_CAO"].KNT07]),
    accessRoles: [],
    path: "/bao-cao/knt07",
    exact: true,
  },
  knt08: {
    component: Page(KNT08, [ROLES["BAO_CAO"].KNT08]),
    accessRoles: [],
    path: "/bao-cao/knt08",
    exact: true,
  },
  knt08_1: {
    component: Page(KNT08_1, [ROLES["BAO_CAO"].KNT08]),
    accessRoles: [],
    path: "/bao-cao/knt08_1",
    exact: true,
  },
  knt10: {
    component: Page(KNT10, [ROLES["BAO_CAO"].KNT10]),
    accessRoles: [ROLES["BAO_CAO"].KNT10],
    path: "/bao-cao/knt10",
    exact: true,
  },
  knt11: {
    component: Page(KNT11, [ROLES["BAO_CAO"].KNT11]),
    accessRoles: [ROLES["BAO_CAO"].KNT11],
    path: "/bao-cao/knt11",
    exact: true,
  },
  knt12: {
    component: Page(KNT12, [ROLES["BAO_CAO"].KNT12]),
    accessRoles: [ROLES["BAO_CAO"].KNT12],
    path: "/bao-cao/knt12",
    exact: true,
  },
  knt13: {
    component: Page(KNT13, [ROLES["BAO_CAO"].KNT13]),
    accessRoles: [ROLES["BAO_CAO"].KNT13],
    path: "/bao-cao/knt13",
    exact: true,
  },
  knt14: {
    component: Page(KNT14, [ROLES["BAO_CAO"].KNT14]),
    accessRoles: [],
    path: "/bao-cao/knt14",
    exact: true,
  },
  knt15: {
    component: Page(KNT15, [ROLES["BAO_CAO"].KNT15]),
    accessRoles: [],
    path: "/bao-cao/knt15",
    exact: true,
  },
  knt15_1: {
    component: Page(KNT15_1, [ROLES["BAO_CAO"].KNT15_1]),
    accessRoles: [],
    path: "/bao-cao/knt15_1",
    exact: true,
  },
  knt16: {
    component: Page(KNT16, [ROLES["BAO_CAO"].KNT16]),
    accessRoles: [],
    path: "/bao-cao/knt16",
    exact: true,
  },
  knt17: {
    component: Page(KNT17, [ROLES["BAO_CAO"].KNT17]),
    accessRoles: [],
    path: "/bao-cao/knt17",
    exact: true,
  },
  knt18: {
    component: Page(KNT18, [ROLES["BAO_CAO"].KNT18]),
    accessRoles: [],
    path: "/bao-cao/knt18",
    exact: true,
  },
  knt19: {
    component: Page(KNT19, [ROLES["BAO_CAO"].KNT19]),
    accessRoles: [ROLES["BAO_CAO"].KNT19],
    path: "/bao-cao/knt19",
    exact: true,
  },
  knt20: {
    component: Page(KNT20, [ROLES["BAO_CAO"].KNT20]),
    accessRoles: [],
    path: "/bao-cao/knt20",
    exact: true,
  },
  knt21: {
    component: Page(KNT21, [ROLES["BAO_CAO"].KNT21]),
    accessRoles: [],
    path: "/bao-cao/knt21",
    exact: true,
  },
  knt22: {
    component: Page(KNT22, [ROLES["BAO_CAO"].KNT22]),
    accessRoles: [ROLES["BAO_CAO"].KNT22],
    path: "/bao-cao/knt22",
    exact: true,
  },
  knt23: {
    component: Page(KNT23, [ROLES["BAO_CAO"].KNT23]),
    accessRoles: [ROLES["BAO_CAO"].KNT23],
    path: "/bao-cao/knt23",
    exact: true,
  },
  knt24: {
    component: Page(KNT24, [ROLES["BAO_CAO"].KNT24]),
    accessRoles: [ROLES["BAO_CAO"].KNT24],
    path: "/bao-cao/knt24",
    exact: true,
  },
  knt25: {
    component: Page(KNT25, [ROLES["BAO_CAO"].KNT25]),
    accessRoles: [ROLES["BAO_CAO"].KNT25],
    path: "/bao-cao/knt25",
    exact: true,
  },
  knt26: {
    component: Page(KNT26, [ROLES["BAO_CAO"].KNT26]),
    accessRoles: [ROLES["BAO_CAO"].KNT26],
    path: "/bao-cao/knt26",
    exact: true,
  },
  ksk01: {
    component: Page(KSK01, [ROLES["BAO_CAO"].KSK01]),
    accessRoles: [],
    path: "/bao-cao/ksk-01",
    exact: true,
  },
  ksk01_1: {
    component: Page(KSK01_1, [ROLES["BAO_CAO"].KSK01_1]),
    accessRoles: [],
    path: "/bao-cao/ksk-01_1",
    exact: true,
  },
  ksk02: {
    component: Page(KSK02, [ROLES["BAO_CAO"].KSK02]),
    accessRoles: [],
    path: "/bao-cao/ksk-02",
    exact: true,
  },
  ksk04: {
    component: Page(KSK04, [ROLES["BAO_CAO"].KSK04]),
    accessRoles: [],
    path: "/bao-cao/ksk-04",
    exact: true,
  },
  ksk05: {
    component: Page(KSK05, [ROLES["BAO_CAO"].KSK05]),
    accessRoles: [],
    path: "/bao-cao/ksk-05",
    exact: true,
  },
  ksk12: {
    component: Page(KSK12, [ROLES["BAO_CAO"].KSK12]),
    accessRoles: [],
    path: "/bao-cao/ksk-12",
    exact: true,
  },
  ksk13: {
    component: Page(KSK13, [ROLES["BAO_CAO"].KSK13]),
    accessRoles: [ROLES["BAO_CAO"].KSK13],
    path: "/bao-cao/ksk-13",
    exact: true,
  },
  ksk15: {
    component: Page(KSK15, [ROLES["BAO_CAO"].KSK15]),
    accessRoles: [ROLES["BAO_CAO"].KSK15],
    path: "/bao-cao/ksk-15",
    exact: true,
  },
  ksk16: {
    component: Page(KSK16, [ROLES["BAO_CAO"].KSK16]),
    accessRoles: [ROLES["BAO_CAO"].KSK16],
    path: "/bao-cao/ksk-16",
    exact: true,
  },
  ksk17: {
    component: Page(KSK17, [ROLES["BAO_CAO"].KSK17]),
    accessRoles: [ROLES["BAO_CAO"].KSK17],
    path: "/bao-cao/ksk-17",
    exact: true,
  },
  ksk18: {
    component: Page(KSK18, [ROLES["BAO_CAO"].KSK18]),
    accessRoles: [ROLES["BAO_CAO"].KSK18],
    path: "/bao-cao/ksk-18",
    exact: true,
  },
  ksk19: {
    component: Page(KSK19, [ROLES["BAO_CAO"].KSK19]),
    accessRoles: [ROLES["BAO_CAO"].KSK19],
    path: "/bao-cao/ksk-19",
    exact: true,
  },
  ksk20: {
    component: Page(KSK20, [ROLES["BAO_CAO"].KSK20]),
    accessRoles: [ROLES["BAO_CAO"].KSK20],
    path: "/bao-cao/ksk-20",
    exact: true,
  },
  ksk20_1: {
    component: Page(KSK20_1, [ROLES["BAO_CAO"].KSK20_1]),
    accessRoles: [ROLES["BAO_CAO"].KSK20_1],
    path: "/bao-cao/ksk-20_1",
    exact: true,
  },
  ksk21: {
    component: Page(KSK21, [ROLES["BAO_CAO"].KSK21]),
    accessRoles: [ROLES["BAO_CAO"].KSK21],
    path: "/bao-cao/ksk-21",
    exact: true,
  },
  g01: {
    component: Page(G01, [ROLES["BAO_CAO"].G01]),
    accessRoles: [],
    path: "/bao-cao/g-01",
    exact: true,
  },
  g02: {
    component: Page(G02, [ROLES["BAO_CAO"].G02]),
    accessRoles: [],
    path: "/bao-cao/g-02",
    exact: true,
  },
  g03: {
    component: Page(G03, [ROLES["BAO_CAO"].G03]),
    accessRoles: [],
    path: "/bao-cao/g-03",
    exact: true,
  },
  g04: {
    component: Page(G04, [ROLES["BAO_CAO"].G04]),
    accessRoles: [ROLES["BAO_CAO"].G04],
    path: "/bao-cao/g-04",
    exact: true,
  },
  g05: {
    component: Page(G05, [ROLES["BAO_CAO"].G05]),
    accessRoles: [ROLES["BAO_CAO"].G05],
    path: "/bao-cao/g-05",
    exact: true,
  },
  g06: {
    component: Page(G06, [ROLES["BAO_CAO"].G06]),
    accessRoles: [ROLES["BAO_CAO"].G06],
    path: "/bao-cao/g-06",
    exact: true,
  },
  g07: {
    component: Page(G07, [ROLES["BAO_CAO"].G07]),
    accessRoles: [ROLES["BAO_CAO"].G07],
    path: "/bao-cao/g-07",
    exact: true,
  },
  khth01: {
    component: Page(KHTH01, [ROLES["BAO_CAO"].KHTH01]),
    accessRoles: [ROLES["BAO_CAO"].KHTH01],
    path: "/bao-cao/khth-01",
    exact: true,
  },
  khth02: {
    component: Page(KHTH02, [ROLES["BAO_CAO"].KHTH02]),
    accessRoles: [ROLES["BAO_CAO"].KHTH02],
    path: "/bao-cao/khth-02",
    exact: true,
  },
  khth03: {
    component: Page(KHTH03, [ROLES["BAO_CAO"].KHTH03]),
    accessRoles: [ROLES["BAO_CAO"].KHTH03],
    path: "/bao-cao/khth-03",
    exact: true,
  },
  khth04: {
    component: Page(KHTH04, [ROLES["BAO_CAO"].KHTH04]),
    accessRoles: [ROLES["BAO_CAO"].KHTH04],
    path: "/bao-cao/khth-04",
    exact: true,
  },
  khth05: {
    component: Page(KHTH05, [ROLES["BAO_CAO"].KHTH05]),
    accessRoles: [ROLES["BAO_CAO"].KHTH05],
    path: "/bao-cao/khth-05",
    exact: true,
  },
  khth06: {
    component: Page(KHTH06, [ROLES["BAO_CAO"].KHTH06]),
    accessRoles: [ROLES["BAO_CAO"].KHTH06],
    path: "/bao-cao/khth-06",
    exact: true,
  },
  khth07: {
    component: Page(KHTH07, [ROLES["BAO_CAO"].KHTH07]),
    accessRoles: [ROLES["BAO_CAO"].KHTH07],
    path: "/bao-cao/khth-07",
    exact: true,
  },
  khth08: {
    component: Page(KHTH08, [ROLES["BAO_CAO"].KHTH08]),
    accessRoles: [ROLES["BAO_CAO"].KHTH08],
    path: "/bao-cao/khth-08",
    exact: true,
  },
  khth08_1: {
    component: Page(KHTH08_1, [ROLES["BAO_CAO"].KHTH08_1]),
    accessRoles: [ROLES["BAO_CAO"].KHTH08_1],
    path: "/bao-cao/khth-08_1",
    exact: true,
  },
  khth08_2: {
    component: Page(KHTH08_2, [ROLES["BAO_CAO"].KHTH08_2]),
    accessRoles: [ROLES["BAO_CAO"].KHTH08_2],
    path: "/bao-cao/khth-08_2",
    exact: true,
  },
  khth09: {
    component: Page(KHTH09, [ROLES["BAO_CAO"].KHTH09]),
    accessRoles: [ROLES["BAO_CAO"].KHTH09],
    path: "/bao-cao/khth-09",
    exact: true,
  },
  khth10: {
    component: Page(KHTH10, [ROLES["BAO_CAO"].KHTH10]),
    accessRoles: [ROLES["BAO_CAO"].KHTH10],
    path: "/bao-cao/khth-10",
    exact: true,
  },
  khth11: {
    component: Page(KHTH11, [ROLES["BAO_CAO"].KHTH11]),
    accessRoles: [ROLES["BAO_CAO"].KHTH11],
    path: "/bao-cao/khth-11",
    exact: true,
  },
  khth12: {
    component: Page(KHTH12, [ROLES["BAO_CAO"].KHTH12]),
    accessRoles: [ROLES["BAO_CAO"].KHTH12],
    path: "/bao-cao/khth-12",
    exact: true,
  },
  khth12_1: {
    component: Page(KHTH12_1, [ROLES["BAO_CAO"].KHTH12_1]),
    accessRoles: [],
    path: "/bao-cao/khth-12_1",
    exact: true,
  },
  khth13: {
    component: Page(KHTH13, [ROLES["BAO_CAO"].KHTH13]),
    accessRoles: [ROLES["BAO_CAO"].KHTH13],
    path: "/bao-cao/khth-13",
    exact: true,
  },
  khth14: {
    component: Page(KHTH14, [ROLES["BAO_CAO"].KHTH14]),
    accessRoles: [ROLES["BAO_CAO"].KHTH14],
    path: "/bao-cao/khth-14",
    exact: true,
  },
  khth15: {
    component: Page(KHTH15, [ROLES["BAO_CAO"].KHTH15]),
    accessRoles: [ROLES["BAO_CAO"].KHTH15],
    path: "/bao-cao/khth-15",
    exact: true,
  },
  khth16: {
    component: Page(KHTH16, [ROLES["BAO_CAO"].KHTH16]),
    accessRoles: [ROLES["BAO_CAO"].KHTH16],
    path: "/bao-cao/khth-16",
    exact: true,
  },
  khth17: {
    component: Page(KHTH17, [ROLES["BAO_CAO"].KHTH17]),
    accessRoles: [ROLES["BAO_CAO"].KHTH17],
    path: "/bao-cao/khth-17",
    exact: true,
  },
  khth18: {
    component: Page(KHTH18, [ROLES["BAO_CAO"].KHTH18]),
    accessRoles: [ROLES["BAO_CAO"].KHTH18],
    path: "/bao-cao/khth-18",
    exact: true,
  },
  khth19: {
    component: Page(KHTH19, [ROLES["BAO_CAO"].KHTH19]),
    accessRoles: [ROLES["BAO_CAO"].KHTH19],
    path: "/bao-cao/khth-19",
    exact: true,
  },
  khth20: {
    component: Page(KHTH20, [ROLES["BAO_CAO"].KHTH20]),
    accessRoles: [ROLES["BAO_CAO"].KHTH20],
    path: "/bao-cao/khth-20",
    exact: true,
  },
  khth21: {
    component: Page(KHTH21, [ROLES["BAO_CAO"].KHTH21]),
    accessRoles: [ROLES["BAO_CAO"].KHTH21],
    path: "/bao-cao/khth-21",
    exact: true,
  },
  khth22: {
    component: Page(KHTH22, [ROLES["BAO_CAO"].KHTH22]),
    accessRoles: [ROLES["BAO_CAO"].KHTH22],
    path: "/bao-cao/khth-22",
    exact: true,
  },
  khth23: {
    component: Page(KHTH23, [ROLES["BAO_CAO"].KHTH23]),
    accessRoles: [ROLES["BAO_CAO"].KHTH23],
    path: "/bao-cao/khth-23",
    exact: true,
  },
  khth24: {
    component: Page(KHTH24, [ROLES["BAO_CAO"].KHTH24]),
    accessRoles: [ROLES["BAO_CAO"].KHTH24],
    path: "/bao-cao/khth-24",
    exact: true,
  },
  khth25: {
    component: Page(KHTH25, [ROLES["BAO_CAO"].KHTH25]),
    accessRoles: [ROLES["BAO_CAO"].KHTH25],
    path: "/bao-cao/khth-25",
    exact: true,
  },
  khth26: {
    component: Page(KHTH26, [ROLES["BAO_CAO"].KHTH26]),
    accessRoles: [ROLES["BAO_CAO"].KHTH26],
    path: "/bao-cao/khth-26",
    exact: true,
  },
  khth27: {
    component: Page(KHTH27, [ROLES["BAO_CAO"].KHTH27]),
    accessRoles: [ROLES["BAO_CAO"].KHTH27],
    path: "/bao-cao/khth-27",
    exact: true,
  },
  khth29: {
    component: Page(KHTH29, [ROLES["BAO_CAO"].KHTH29]),
    accessRoles: [ROLES["BAO_CAO"].KHTH29],
    path: "/bao-cao/khth-29",
    exact: true,
  },
  khth30: {
    component: Page(KHTH30, [ROLES["BAO_CAO"].KHTH30]),
    accessRoles: [ROLES["BAO_CAO"].KHTH30],
    path: "/bao-cao/khth-30",
    exact: true,
  },
  khth31: {
    component: Page(KHTH31, [ROLES["BAO_CAO"].KHTH31]),
    accessRoles: [ROLES["BAO_CAO"].KHTH31],
    path: "/bao-cao/khth-31",
    exact: true,
  },
  khth33: {
    component: Page(KHTH33, [ROLES["BAO_CAO"].KHTH33]),
    accessRoles: [ROLES["BAO_CAO"].KHTH33],
    path: "/bao-cao/khth-33",
    exact: true,
  },
  khth34: {
    component: Page(KHTH34, [ROLES["BAO_CAO"].KHTH34]),
    accessRoles: [ROLES["BAO_CAO"].KHTH34],
    path: "/bao-cao/khth-34",
    exact: true,
  },
  khth35: {
    component: Page(KHTH35, [ROLES["BAO_CAO"].KHTH35]),
    accessRoles: [ROLES["BAO_CAO"].KHTH35],
    path: "/bao-cao/khth-35",
    exact: true,
  },
  khth37: {
    component: Page(KHTH37, [ROLES["BAO_CAO"].KHTH37]),
    accessRoles: [ROLES["BAO_CAO"].KHTH37],
    path: "/bao-cao/khth-37",
    exact: true,
  },
  khth38: {
    component: Page(KHTH38, [ROLES["BAO_CAO"].KHTH38]),
    accessRoles: [ROLES["BAO_CAO"].KHTH38],
    path: "/bao-cao/khth-38",
    exact: true,
  },
  khth40: {
    component: Page(KHTH40, [ROLES["BAO_CAO"].KHTH40]),
    accessRoles: [ROLES["BAO_CAO"].KHTH40],
    path: "/bao-cao/khth-40",
    exact: true,
  },
  khth41: {
    component: Page(KHTH41, [ROLES["BAO_CAO"].KHTH41]),
    accessRoles: [ROLES["BAO_CAO"].KHTH41],
    path: "/bao-cao/khth-41",
    exact: true,
  },
  khth42: {
    component: Page(KHTH42, [ROLES["BAO_CAO"].KHTH42]),
    accessRoles: [ROLES["BAO_CAO"].KHTH42],
    path: "/bao-cao/khth-42",
    exact: true,
  },
  khth43: {
    component: Page(KHTH43, [ROLES["BAO_CAO"].KHTH43]),
    accessRoles: [ROLES["BAO_CAO"].KHTH43],
    path: "/bao-cao/khth-43",
    exact: true,
  },
  khth44: {
    component: Page(KHTH44, [ROLES["BAO_CAO"].KHTH44]),
    accessRoles: [ROLES["BAO_CAO"].KHTH44],
    path: "/bao-cao/khth-44",
    exact: true,
  },
  khth45: {
    component: Page(KHTH45, [ROLES["BAO_CAO"].KHTH45]),
    accessRoles: [ROLES["BAO_CAO"].KHTH45],
    path: "/bao-cao/khth-45",
    exact: true,
  },
  khth46: {
    component: Page(KHTH46, [ROLES["BAO_CAO"].KHTH46]),
    accessRoles: [ROLES["BAO_CAO"].KHTH46],
    path: "/bao-cao/khth-46",
    exact: true,
  },
  khth47: {
    component: Page(KHTH47, [ROLES["BAO_CAO"].KHTH47]),
    accessRoles: [ROLES["BAO_CAO"].KHTH47],
    path: "/bao-cao/khth-47",
    exact: true,
  },
  khth48: {
    component: Page(KHTH48, [ROLES["BAO_CAO"].KHTH48]),
    accessRoles: [ROLES["BAO_CAO"].KHTH48],
    path: "/bao-cao/khth-48",
    exact: true,
  },
  khth49: {
    component: Page(KHTH49, [ROLES["BAO_CAO"].KHTH49]),
    accessRoles: [ROLES["BAO_CAO"].KHTH49],
    path: "/bao-cao/khth-49",
    exact: true,
  },
  pttt01: {
    component: Page(PTTT01, [ROLES["BAO_CAO"].PTTT01]),
    accessRoles: [],
    path: "/bao-cao/pttt-01",
    exact: true,
  },
  pttt02: {
    component: Page(PTTT02, [ROLES["BAO_CAO"].PTTT02]),
    accessRoles: [ROLES["BAO_CAO"].PTTT02],
    path: "/bao-cao/pttt-02",
    exact: true,
  },
  pttt03: {
    component: Page(PTTT03, [ROLES["BAO_CAO"].PTTT03]),
    accessRoles: [ROLES["BAO_CAO"].PTTT03],
    path: "/bao-cao/pttt-03",
    exact: true,
  },
  pttt04: {
    component: Page(PTTT04, [ROLES["BAO_CAO"].PTTT04]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04],
    path: "/bao-cao/pttt-04",
    exact: true,
  },
  pttt04_1: {
    component: Page(PTTT04_1, [ROLES["BAO_CAO"].PTTT04_01]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_01],
    path: "/bao-cao/pttt-04_1",
    exact: true,
  },
  pttt04_2: {
    component: Page(PTTT04_2, [ROLES["BAO_CAO"].PTTT04_02]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_02],
    path: "/bao-cao/pttt-04_2",
    exact: true,
  },
  pttt04_3: {
    component: Page(PTTT04_3, [ROLES["BAO_CAO"].PTTT04_03]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_03],
    path: "/bao-cao/pttt-04_3",
    exact: true,
  },
  pttt04_4: {
    component: Page(PTTT04_4, [ROLES["BAO_CAO"].PTTT04_04]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_04],
    path: "/bao-cao/pttt-04_4",
    exact: true,
  },
  pttt04_5: {
    component: Page(PTTT04_5, [ROLES["BAO_CAO"].PTTT04_05]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_05],
    path: "/bao-cao/pttt-04_5",
    exact: true,
  },
  pttt04_7: {
    component: Page(PTTT04_7, [ROLES["BAO_CAO"].PTTT04_07]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_07],
    path: "/bao-cao/pttt-04_7",
    exact: true,
  },
  pttt04_8: {
    component: Page(PTTT04_8, [ROLES["BAO_CAO"].PTTT04_08]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_08],
    path: "/bao-cao/pttt-04_8",
    exact: true,
  },
  pttt04_9: {
    component: Page(PTTT04_9, [ROLES["BAO_CAO"].PTTT04_09]),
    accessRoles: [ROLES["BAO_CAO"].PTTT04_09],
    path: "/bao-cao/pttt-04_9",
    exact: true,
  },
  pttt05: {
    component: Page(PTTT05, [ROLES["BAO_CAO"].PTTT05]),
    accessRoles: [ROLES["BAO_CAO"].PTTT05],
    path: "/bao-cao/pttt-05",
    exact: true,
  },
  pttt06: {
    component: Page(PTTT06, [ROLES["BAO_CAO"].PTTT06]),
    accessRoles: [ROLES["BAO_CAO"].PTTT06],
    path: "/bao-cao/pttt-06",
    exact: true,
  },
  pttt07: {
    component: Page(PTTT07, [ROLES["BAO_CAO"].PTTT07]),
    accessRoles: [ROLES["BAO_CAO"].PTTT07],
    path: "/bao-cao/pttt-07",
    exact: true,
  },
  pttt08: {
    component: Page(PTTT08, [ROLES["BAO_CAO"].PTTT08]),
    accessRoles: [ROLES["BAO_CAO"].PTTT08],
    path: "/bao-cao/pttt-08",
    exact: true,
  },
  pttt09: {
    component: Page(PTTT09, [ROLES["BAO_CAO"].PTTT09]),
    accessRoles: [ROLES["BAO_CAO"].PTTT09],
    path: "/bao-cao/pttt-09",
    exact: true,
  },
  dd01: {
    component: Page(DD01, [ROLES["BAO_CAO"].DD01]),
    accessRoles: [ROLES["BAO_CAO"].DD01],
    path: "/bao-cao/dd-01",
    exact: true,
  },
  dd03: {
    component: Page(DD03, [ROLES["BAO_CAO"].DD03]),
    accessRoles: [ROLES["BAO_CAO"].DD03],
    path: "/bao-cao/dd-03",
    exact: true,
  },
  //tiêm chủng
  tiem01: {
    component: Page(TIEM01, [ROLES["BAO_CAO"].TIEM_01]),
    accessRoles: [ROLES["BAO_CAO"].TIEM_01],
    path: "/bao-cao/tiem-01",
    exact: true,
  },
  tiem02: {
    component: Page(TIEM02, [ROLES["BAO_CAO"].TIEM_02]),
    accessRoles: [ROLES["BAO_CAO"].TIEM_02],
    path: "/bao-cao/tiem-02",
    exact: true,
  },
  tiem03: {
    component: Page(TIEM03, [ROLES["BAO_CAO"].TIEM_03]),
    accessRoles: [ROLES["BAO_CAO"].TIEM_03],
    path: "/bao-cao/tiem-03",
    exact: true,
  },
  tiem04: {
    component: Page(TIEM04, [ROLES["BAO_CAO"].TIEM_04]),
    accessRoles: [ROLES["BAO_CAO"].TIEM_04],
    path: "/bao-cao/tiem-04",
    exact: true,
  },
  tiem05: {
    component: Page(TIEM05, [ROLES["BAO_CAO"].TIEM_05]),
    accessRoles: [ROLES["BAO_CAO"].TIEM_05],
    path: "/bao-cao/tiem-05",
    exact: true,
  },
  KSNK_01: {
    component: Page(KSNK_01, [ROLES["BAO_CAO"].KSNK_01]),
    accessRoles: [ROLES["BAO_CAO"].KSNK_01],
    path: "/bao-cao/ksnk_01",
    exact: true,
  },
  KSNK_02: {
    component: Page(KSNK_02, [ROLES["BAO_CAO"].KSNK_02]),
    accessRoles: [ROLES["BAO_CAO"].KSNK_02],
    path: "/bao-cao/ksnk_02",
    exact: true,
  },
  LAO01: {
    component: Page(LAO01, [ROLES["BAO_CAO"].LAO01]),
    accessRoles: [ROLES["BAO_CAO"].LAO01],
    path: "/bao-cao/lao01",
    exact: true,
  },
  KDD01: {
    component: Page(KDD01, [ROLES["BAO_CAO"].KDD01]),
    accessRoles: [ROLES["BAO_CAO"].KDD01],
    path: "/bao-cao/kdd01",
    exact: true,
  },
  KDD02: {
    component: Page(KDD02, [ROLES["BAO_CAO"].KDD02]),
    accessRoles: [ROLES["BAO_CAO"].KDD02],
    path: "/bao-cao/kdd02",
    exact: true,
  },
  KDD03: {
    component: Page(KDD03, [ROLES["BAO_CAO"].KDD03]),
    accessRoles: [ROLES["BAO_CAO"].KDD03],
    path: "/bao-cao/kdd03",
    exact: true,
  },
  KDD04: {
    component: Page(KDD04, [ROLES["BAO_CAO"].KDD04]),
    accessRoles: [ROLES["BAO_CAO"].KDD04],
    path: "/bao-cao/kdd04",
    exact: true,
  },
  KDD05: {
    component: Page(KDD05, [ROLES["BAO_CAO"].KDD05]),
    accessRoles: [ROLES["BAO_CAO"].KDD05],
    path: "/bao-cao/kdd05",
    exact: true,
  },
  KDD06: {
    component: Page(KDD06, [ROLES["BAO_CAO"].KDD06]),
    accessRoles: [],
    path: "/bao-cao/kdd06",
  },
  KDD07: {
    component: Page(KDD07, [ROLES["BAO_CAO"].KDD07]),
    accessRoles: [],
    path: "/bao-cao/kdd07",
    exact: true,
  },
  KDD08: {
    component: Page(KDD08, [ROLES["BAO_CAO"].KDD08]),
    accessRoles: [ROLES["BAO_CAO"].KDD08],
    path: "/bao-cao/kdd08",
    exact: true,
  },
  KDD09: {
    component: Page(KDD09, [ROLES["BAO_CAO"].KDD09]),
    accessRoles: [ROLES["BAO_CAO"].KDD09],
    path: "/bao-cao/kdd09",
    exact: true,
  },
  KDD10: {
    component: Page(KDD10, [ROLES["BAO_CAO"].KDD10]),
    accessRoles: [ROLES["BAO_CAO"].KDD10],
    path: "/bao-cao/kdd10",
    exact: true,
  },
  KDD11: {
    component: Page(KDD11, [ROLES["BAO_CAO"].KDD11]),
    accessRoles: [ROLES["BAO_CAO"].KDD11],
    path: "/bao-cao/kdd11",
    exact: true,
  },
  KDD12: {
    component: Page(KDD12, [ROLES["BAO_CAO"].KDD12]),
    accessRoles: [ROLES["BAO_CAO"].KDD12],
    path: "/bao-cao/kdd12",
    exact: true,
  },
  KDD13: {
    component: Page(KDD13, [ROLES["BAO_CAO"].KDD13]),
    accessRoles: [],
    path: "/bao-cao/kdd13",
    exact: true,
  },
  KDD14: {
    component: Page(KDD14, [ROLES["BAO_CAO"].KDD14]),
    accessRoles: [],
    path: "/bao-cao/kdd14",
    exact: true,
  },

  QT01: {
    component: Page(QT01, [ROLES["BAO_CAO"].QT01]),
    accessRoles: [ROLES["BAO_CAO"].QT01],
    path: "/bao-cao/qt01",
    exact: true,
  },
  QT02: {
    component: Page(QT02, [ROLES["BAO_CAO"].QT02]),
    accessRoles: [ROLES["BAO_CAO"].QT02],
    path: "/bao-cao/qt02",
    exact: true,
  },
  QT03: {
    component: Page(QT03, [ROLES["BAO_CAO"].QT03]),
    accessRoles: [ROLES["BAO_CAO"].QT03],
    path: "/bao-cao/qt03",
    exact: true,
  },
  QTBH13: {
    component: Page(QTBH13, [ROLES["BAO_CAO"].QTBH13]),
    accessRoles: [ROLES["BAO_CAO"].QTBH13],
    path: "/bao-cao/qtbh13",
    exact: true,
  },
  DDLS05: {
    component: Page(DDLS05, [ROLES["BAO_CAO"].DDLS05]),
    accessRoles: [ROLES["BAO_CAO"].DDLS05],
    path: "/bao-cao/ddls05",
    exact: true,
  },
  DDLS06: {
    component: Page(DDLS06, [ROLES["BAO_CAO"].DDLS06]),
    accessRoles: [ROLES["BAO_CAO"].DDLS06],
    path: "/bao-cao/ddls06",
    exact: true,
  },
  DDLS07: {
    component: Page(DDLS07, [ROLES["BAO_CAO"].DDLS07]),
    accessRoles: [ROLES["BAO_CAO"].DDLS07],
    path: "/bao-cao/ddls07",
    exact: true,
  },
  DDLS08: {
    component: Page(DDLS08, [ROLES["BAO_CAO"].DDLS08]),
    accessRoles: [ROLES["BAO_CAO"].DDLS08],
    path: "/bao-cao/ddls08",
    exact: true,
  },
  DDLS09: {
    component: Page(DDLS09, [ROLES["BAO_CAO"].DDLS09]),
    accessRoles: [ROLES["BAO_CAO"].DDLS09],
    path: "/bao-cao/ddls09",
    exact: true,
  },
  DDLS10: {
    component: Page(DDLS10, [ROLES["BAO_CAO"].DDLS10]),
    accessRoles: [ROLES["BAO_CAO"].DDLS10],
    path: "/bao-cao/ddls10",
    exact: true,
  },
  SLDD01: {
    component: Page(SLDD01, [ROLES["BAO_CAO"].SLDD01]),
    accessRoles: [ROLES["BAO_CAO"].SLDD01],
    path: "/bao-cao/sldd01",
    exact: true,
  },
  SLDD02: {
    component: Page(SLDD02, [ROLES["BAO_CAO"].SLDD02]),
    accessRoles: [ROLES["BAO_CAO"].SLDD02],
    path: "/bao-cao/sldd02",
    exact: true,
  },
  SLDD03: {
    component: Page(SLDD03, [ROLES["BAO_CAO"].SLDD03]),
    accessRoles: [ROLES["BAO_CAO"].SLDD03],
    path: "/bao-cao/sldd03",
    exact: true,
  },
  SLDD05: {
    component: Page(SLDD05, [ROLES["BAO_CAO"].SLDD05]),
    accessRoles: [ROLES["BAO_CAO"].SLDD05],
    path: "/bao-cao/sldd05",
    exact: true,
  },
  SLDD06: {
    component: Page(SLDD06, [ROLES["BAO_CAO"].SLDD06]),
    accessRoles: [ROLES["BAO_CAO"].SLDD06],
    path: "/bao-cao/sldd06",
    exact: true,
  },
};
