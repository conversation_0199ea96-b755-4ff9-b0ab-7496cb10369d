import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import { checkRoleOr } from "lib-utils/role-utils";
import React from "react";

const SubPagePhaCheThuoc = React.lazy(() =>
  import("pages/home/<USER>/PhaCheThuoc")
);
const CongThucPhaChe = React.lazy(() =>
  import("pages/phaCheThuoc/CongThucPhaChe")
);
const DanhSachPhaCheThuoc = React.lazy(() =>
  import("pages/phaCheThuoc/DanhSachPhaCheThuoc")
);
const DanhSachPhaCheThuocTheoDon = React.lazy(() =>
  import("pages/phaCheThuoc/DanhSachPhaCheThuocTheoDon")
);

const PhieuPhaCheThuoc = React.lazy(() =>
  import("pages/phaCheThuoc/PhieuPhaCheThuoc")
);
const DanhSachPhieuXuatPhaCheThuoc = React.lazy(() =>
  import("pages/phaCheThuoc/DanhSachPhieuXuatPhaChe")
);
const PhieuXuatPhaCheThuoc = React.lazy(() =>
  import("pages/phaCheThuoc/PhieuXuatPhaCheThuoc")
);
const DanhSachPhieuChotPhaChe = React.lazy(() =>
  import("pages/phaCheThuoc/DanhSachPhieuChotPhaChe")
);
const PhieuChotPhaCheThuoc = React.lazy(() =>
  import("pages/phaCheThuoc/PhieuChotPhaCheThuoc")
);

const renderPage = () => {
  const rs = {
    phieuXuatPhaCheThuoc: {
      component: Page(PhieuXuatPhaCheThuoc, []),
      accessRoles: [],
      path: [
        "/pha-che-thuoc/phieu-xuat-pha-che-thuoc/them-moi",
        "/pha-che-thuoc/phieu-xuat-pha-che-thuoc/chi-tiet/:id",
        "/pha-che-thuoc/phieu-xuat-pha-che-thuoc/chinh-sua/:id",
      ],
      exact: true,
    },
    phieuPhaCheThuoc: {
      component: Page(PhieuPhaCheThuoc, []),
      // accessRoles: [ROLES["PHA_CHE_THUOC"].XEM_CHI_TIET_PHIEU_PHA_CHE_THUOC],
      accessRoles: [],
      path: [
        "/pha-che-thuoc/phieu-pha-che-thuoc/them-moi",
        "/pha-che-thuoc/phieu-pha-che-thuoc/chi-tiet/:id",
        "/pha-che-thuoc/phieu-pha-che-thuoc/chinh-sua/:id",
      ],
      exact: true,
    },
    danhSachPhieuChotPhaChe: {
      component: Page(DanhSachPhieuChotPhaChe, []),
      accessRoles: [],
      path: ["/pha-che-thuoc/danh-sach-phieu-chot-pha-che"],
      exact: true,
    },
    phieuChotPhaCheThuoc: {
      component: Page(PhieuChotPhaCheThuoc, []),
      accessRoles: [],
      path: [
        "/pha-che-thuoc/phieu-chot-pha-che-thuoc/chi-tiet/:id/:nbDotDieuTriId",
      ],
      exact: true,
    },
  };
  if (checkRoleOr([ROLES["PHA_CHE_THUOC"].PHA_CHE_THUOC])) {
    rs.subPagePhaCheThuoc = {
      component: Page(SubPagePhaCheThuoc, []),
      accessRoles: [ROLES["PHA_CHE_THUOC"].PHA_CHE_THUOC],
      path: "/pha-che-thuoc",
      exact: true,
    };
  }
  if (checkRoleOr([ROLES["PHA_CHE_THUOC"].CONG_THUC_PHA_CHE])) {
    rs.congThucPhaChe = {
      component: Page(CongThucPhaChe, []),
      accessRoles: [],
      path: ["/pha-che-thuoc/cong-thuc-pha-che"],
      exact: true,
    };
  }
  if (
    checkRoleOr([
      ROLES["PHA_CHE_THUOC"].DANH_SACH_PHA_CHE,
      ROLES["PHA_CHE"].XEM_CHI_TIET_PHIEU_PHA_CHE_THUOC,
      ROLES["PHA_CHE"].THEM_PHIEU_PHA_CHE_THUOC,
      ROLES["PHA_CHE"].SUA_PHIEU_PHA_CHE_THUOC,
      ROLES["PHA_CHE"].XOA_PHIEU_PHA_CHE_THUOC,
      ROLES["PHA_CHE"].IN_PHIEU_DICH_PHA,
      ROLES["PHA_CHE"].IN_PHIEU_PHA_CHE,
      ROLES["PHA_CHE"].CHUYEN_PHA_CHE,
      ROLES["PHA_CHE"].HUY_CHUYEN_PHA_CHE,
      ROLES["PHA_CHE"].DUYET_PHA_CHE,
      ROLES["PHA_CHE"].HUY_DUYET_PHA_CHE,
      ROLES["PHA_CHE"].PHA_CHE,
      ROLES["PHA_CHE"].GIAO_PHA_CHE,
      ROLES["PHA_CHE"].HUY_GIAO_PHA_CHE,
    ])
  ) {
    rs.danhSachPhaCheThuoc = {
      component: Page(DanhSachPhaCheThuoc, []),
      accessRoles: [],
      path: ["/pha-che-thuoc/danh-sach-pha-che-thuoc"],
      exact: true,
    };
  }
  if (
    checkRoleOr([
      ROLES["PHA_CHE_THUOC"].DANH_SACH_PHIEU_XUAT_PHA_CHE,
      ROLES["PHA_CHE_THUOC"].XEM_CHI_TIET_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].SUA_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].XOA_PHIEU_XUAT_PHA_CHE_THUOC,
      ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_KHO,
      ROLES["PHA_CHE_THUOC"].IN_PHIEU_XUAT_PHA_CHE,
      ROLES["PHA_CHE_THUOC"].IN_TEM_SAN_PHAM_PHA_CHE_HANG_LOAT,
      ROLES["PHA_CHE_THUOC"].HOAN_THANH,
      ROLES["PHA_CHE_THUOC"].HUY_HOAN_THANH,
    ])
  ) {
    rs.danhSachPhieuXuatPhaCheThuoc = {
      component: Page(DanhSachPhieuXuatPhaCheThuoc, []),
      accessRoles: [],
      path: ["/pha-che-thuoc/danh-sach-phieu-xuat-pha-che-thuoc"],
      exact: true,
    };
  }
  rs.danhSachPhaCheThuocTheoDon = {
    component: Page(DanhSachPhaCheThuocTheoDon, []),
    accessRoles: [],
    path: ["/pha-che-thuoc/danh-sach-pha-che-thuoc-theo-don"],
    exact: true,
  };
  return rs;
};

export default { ...renderPage() };
